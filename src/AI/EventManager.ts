import { Client, Events, Guild, GuildMember, PartialGuildMember } from 'discord.js';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';

export class EventManager {
  private static instance: EventManager;
  private client!: Client; // Definite assignment assertion - set via setClient()
  private readonly config: ConfigManager;
  private readonly startTime: Date;

  private constructor() {
    this.config = ConfigManager.getInstance();
    this.startTime = new Date();
  }

  public static getInstance(): EventManager {
    if (!EventManager.instance) {
      EventManager.instance = new EventManager();
    }
    return EventManager.instance;
  }

  public setClient(client: Client): void {
    this.client = client;
    this.registerEvents();
  }

  private registerEvents(): void {
    // Ready event
    this.client.on(Events.ClientReady, this.onReady.bind(this));
    
    // Guild events
    this.client.on(Events.GuildCreate, this.onGuildJoin.bind(this));
    this.client.on(Events.GuildDelete, this.onGuildLeave.bind(this));
    
    // Member events
    this.client.on(Events.GuildMemberAdd, this.onMemberJoin.bind(this));
    this.client.on(Events.GuildMemberRemove, this.onMemberLeave.bind(this));
    
    // Error events
    this.client.on(Events.Error, this.onError.bind(this));
    this.client.on(Events.Warn, this.onWarn.bind(this));
    
    // Rate limit events - Note: RateLimited event was removed in Discord.js v14
    // this.client.on(Events.RateLimited, this.onRateLimit.bind(this));
    
    // Debug events (only in development)
    if (process.env.NODE_ENV === 'development') {
      this.client.on(Events.Debug, this.onDebug.bind(this));
    }

    console.log('📡 Event listeners registered successfully!');
  }

  private async onReady(client: Client<true>): Promise<void> {
    const settings = this.config.getSettings();
    
    console.log(`
🌟✨ Amy is Ready! ✨🌟
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
👤 Logged in as: ${client.user.tag}
🆔 User ID: ${client.user.id}
🏠 Servers: ${client.guilds.cache.size}
👥 Total Users: ${client.users.cache.size}
⚡ Prefix: ${settings.prefix}
🤖 AI Enabled: ${settings.ai_enabled ? '✅' : '❌'}
🔧 Selfbot Enabled: ${settings.selfbot_enabled ? '✅' : '❌'}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    `);

    if (!settings.selfbot_enabled) {
      console.log('⚠️  Amy is currently disabled. Use the selfbot command to enable.');
      await client.user.setPresence({
        activities: [{
          name: 'sleeping... 💤',
          type: 0
        }],
        status: 'idle'
      });
      return;
    }

    // Set anime girl presence
    await client.user.setPresence({
      activities: [{
        name: 'with Boss-sama ✨',
        type: 0
      }],
      status: 'online'
    });

    // Display startup stats
    this.displayStartupStats();
  }

  private async onGuildJoin(guild: Guild): Promise<void> {
    console.log(`✨ Joined new server: ${guild.name} (${guild.id})`);
    console.log(`👥 Members: ${guild.memberCount}`);
    
    // Optional: Send a greeting if Amy has permission
    try {
      const systemChannel = guild.systemChannel;
      if (systemChannel && systemChannel.permissionsFor(this.client.user!)?.has('SendMessages')) {
        const greetings = [
          "Yo! Amy-chan has arrived! Ready to bring some anime vibes! ✨",
          "Ohayo! New server, new adventures! Let's make it lit! 💥",
          "Hey there! Amy is here to slay and spread kawaii energy! 🌟"
        ];
        
        const randomGreeting = greetings[Math.floor(Math.random() * greetings.length)];
        await systemChannel.send(AmyPersonality.addPersonalityToMessage(randomGreeting));
      }
    } catch (error) {
      console.log('Could not send greeting message (no permissions)');
    }
  }

  private async onGuildLeave(guild: Guild): Promise<void> {
    console.log(`👋 Left server: ${guild.name} (${guild.id})`);
  }

  private async onMemberJoin(member: GuildMember): Promise<void> {
    // Log new member joins (could be extended for welcome messages)
    console.log(`👋 New member joined ${member.guild.name}: ${member.user.tag}`);
  }

  private async onMemberLeave(member: GuildMember | PartialGuildMember): Promise<void> {
    // Log member leaves - member can be partial in some cases
    try {
      const guild = member.guild;
      const user = member.user || member.partial ? await member.fetch().then(m => m.user) : null;
      if (user) {
        console.log(`👋 Member left ${guild.name}: ${user.tag}`);
      } else {
        console.log(`👋 Member left ${guild.name}: Unknown user`);
      }
    } catch (error) {
      console.log(`👋 Member left a server: Unable to fetch details`);
    }
  }

  private async onError(error: Error): Promise<void> {
    console.error('🚨 Discord Client Error:', error);
    
    // Log with anime personality
    const errorMessages = [
      "Oopsie! Discord-kun is having issues! 🥺",
      "Error-chan appeared in the Discord connection! 💥",
      "Something's not vibing with Discord today! 🤔"
    ];
    
    const randomError = errorMessages[Math.floor(Math.random() * errorMessages.length)];
    console.log(randomError);
  }

  private async onWarn(warning: string): Promise<void> {
    console.warn('⚠️  Discord Warning:', warning);
  }

  // Rate limit handling removed - Discord.js v14 no longer emits RateLimited events

  private async onDebug(info: string): Promise<void> {
    // Only log important debug info to avoid spam
    if (info.includes('Heartbeat') || info.includes('Session') || info.includes('Ready')) {
      console.debug('🔍 Debug:', info);
    }
  }

  private displayStartupStats(): void {
    const uptime = Date.now() - this.startTime.getTime();
    const guilds = this.client.guilds.cache;
    const users = this.client.users.cache;
    
    console.log(`
📊 Amy's Current Stats:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏱️  Startup Time: ${uptime}ms
🏠 Servers: ${guilds.size}
👥 Cached Users: ${users.size}
📝 Cached Channels: ${this.client.channels.cache.size}
🎭 Cached Emojis: ${this.client.emojis.cache.size}
💾 Memory Usage: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    `);

    // Display largest servers
    const largestGuilds = guilds.sort((a, b) => b.memberCount - a.memberCount).first(3);
    if (largestGuilds.length > 0) {
      console.log('🏆 Largest Servers:');
      largestGuilds.forEach((guild, index) => {
        console.log(`   ${index + 1}. ${guild.name} (${guild.memberCount} members)`);
      });
    }
  }

  public getUptime(): number {
    return Date.now() - this.startTime.getTime();
  }

  public getStartTime(): Date {
    return this.startTime;
  }

  public async updatePresence(activity?: string, status?: 'online' | 'idle' | 'dnd' | 'invisible'): Promise<void> {
    const settings = this.config.getSettings();

    if (!settings.selfbot_enabled) {
      await this.client.user?.setPresence({
        activities: [{
          name: 'sleeping... 💤',
          type: 0
        }],
        status: 'idle'
      });
      return;
    }

    await this.client.user?.setPresence({
      activities: [{
        name: activity || 'with Boss-sama ✨',
        type: 0
      }],
      status: status || 'online'
    });
  }

  public getClientStats(): any {
    return {
      uptime: this.getUptime(),
      guilds: this.client.guilds.cache.size,
      users: this.client.users.cache.size,
      channels: this.client.channels.cache.size,
      emojis: this.client.emojis.cache.size,
      ping: this.client.ws.ping,
      memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024)
    };
  }
}
