import { Client, Message, ActivityType, TextBasedChannel, DMChannel, TextChannel, NewsChannel, ThreadChannel } from 'discord.js';
import { Discord, On } from 'discordx';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';
import { ShapesIntegration } from '../utils/ShapesIntegration';
import { CommandContext } from '../types';
import { CommandHandler } from './CommandHandler';
import { EventManager } from './EventManager';

@Discord()
export class AmyBot {
  private readonly config: ConfigManager;
  private readonly shapes: ShapesIntegration;
  private readonly commandHandler: CommandHandler;
  private readonly eventManager: EventManager;
  private client!: Client; // Definite assignment assertion - set via setClient()

  constructor() {
    this.config = ConfigManager.getInstance();
    this.shapes = ShapesIntegration.getInstance();
    this.commandHandler = CommandHandler.getInstance();
    this.eventManager = EventManager.getInstance();
  }

  public setClient(client: Client): void {
    this.client = client;
    this.commandHandler.setClient(client);
    this.eventManager.setClient(client);
  }

  // Ready event is now handled by EventManager

  @On({ event: 'messageCreate' })
  async onMessage(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    // Ignore own messages and system messages
    if (message.author.id === this.client.user?.id || message.system) {
      return;
    }

    // Check if selfbot is enabled
    if (!settings.selfbot_enabled) {
      return;
    }

    const context = this.createCommandContext(message);

    // Handle commands
    if (message.content.startsWith(settings.prefix)) {
      await this.handleCommand(message, context);
      return;
    }

    // Handle AI responses when enabled
    if (settings.ai_enabled && this.shouldRespondWithAI(message)) {
      await this.handleAIResponse(message, context);
    }
  }

  private createCommandContext(message: Message): CommandContext {
    const settings = this.config.getSettings();
    
    return {
      userId: message.author.id,
      username: message.author.username,
      channelId: message.channel.id,
      guildId: message.guild?.id,
      isWhitelisted: this.config.isWhitelisted(message.author.id)
    };
  }

  private async handleCommand(message: Message, context: CommandContext): Promise<void> {
    // Use the command handler for better command management
    await this.commandHandler.handleCommand(message, context);
  }

  private shouldRespondWithAI(message: Message): boolean {
    // Respond to mentions or DMs
    const isMentioned = message.mentions.has(this.client.user!);
    const isDM = message.channel.type === 1; // DM channel
    
    // Random chance to respond in servers (5% chance)
    const randomResponse = Math.random() < 0.05;
    
    return isMentioned || isDM || randomResponse;
  }

  private async handleAIResponse(message: Message, context: CommandContext): Promise<void> {
    try {
      // Show typing indicator with proper type guard
      if (this.isTextBasedChannel(message.channel)) {
        await message.channel.sendTyping();
      }

      // Generate stream-of-consciousness response
      const streamMessages = await this.shapes.generateStreamResponse(
        message.content,
        `User: ${context.username} in ${message.guild?.name || 'DM'}`
      );

      // Send messages with delays to simulate natural conversation flow
      let totalDelay = 0;
      for (let i = 0; i < streamMessages.length; i++) {
        const streamMsg = streamMessages[i];
        totalDelay += streamMsg.delay;

        setTimeout(async () => {
          try {
            // Show typing before each message for realism
            if (this.isTextBasedChannel(message.channel)) {
              await message.channel.sendTyping();
            }

            // Wait a bit more for typing effect
            setTimeout(async () => {
              if (this.isTextBasedChannel(message.channel)) {
                await message.channel.send(streamMsg.content);
              }
            }, 1000);

          } catch (error) {
            console.error('Failed to send AI response:', error);
          }
        }, totalDelay);
      }

      // Update character memory after all messages are sent
      setTimeout(async () => {
        await this.shapes.updateCharacterMemory(
          message.content,
          streamMessages.map(m => m.content).join(' ')
        );
      }, totalDelay + 2000);

    } catch (error) {
      console.error('AI response error:', error);
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
    }
  }

  public async handleError(error: Error): Promise<void> {
    console.error('Amy encountered an error:', error);
    
    // Log error with anime personality
    const errorMessages = [
      "Oopsie! Amy-chan encountered a glitch! 🥺",
      "Error-kun appeared! Time to debug! 💥",
      "Something's not vibing right in the code! 🤔"
    ];
    
    const randomError = errorMessages[Math.floor(Math.random() * errorMessages.length)];
    console.log(randomError);
  }

  public async shutdown(): Promise<void> {
    console.log('Amy is going offline... Sayonara! ✨');
    await this.client.destroy();
  }

  public getCommandHandler(): CommandHandler {
    return this.commandHandler;
  }

  public getEventManager(): EventManager {
    return this.eventManager;
  }

  public getClient(): Client {
    return this.client;
  }

  public async updatePresence(activity?: string, status?: 'online' | 'idle' | 'dnd' | 'invisible'): Promise<void> {
    await this.eventManager.updatePresence(activity, status);
  }

  public getStats(): any {
    return {
      ...this.eventManager.getClientStats(),
      commands: this.commandHandler.getCommands().size,
      aiEnabled: this.config.getSettings().ai_enabled,
      selfbotEnabled: this.config.getSettings().selfbot_enabled,
      prefix: this.config.getSettings().prefix
    };
  }

  /**
   * Type guard to check if a channel supports text-based operations
   */
  private isTextBasedChannel(channel: any): channel is TextBasedChannel & {
    sendTyping(): Promise<void>;
    send(content: string): Promise<Message>;
  } {
    return channel &&
           typeof channel.send === 'function' &&
           typeof channel.sendTyping === 'function' &&
           channel.type !== undefined &&
           channel.type !== 3; // Exclude PartialGroupDMChannel (type 3)
  }
}
