import { Client } from 'discord.js';
import { ConfigManager } from '../utils/ConfigManager';
import { ShapesIntegration } from '../utils/ShapesIntegration';
import axios from 'axios';

export interface HealthStatus {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  components: {
    discord: ComponentHealth;
    config: ComponentHealth;
    shapes: ComponentHealth;
    memory: ComponentHealth;
    commands: ComponentHealth;
  };
  timestamp: string;
  uptime: number;
}

export interface ComponentHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  message: string;
  details?: any;
}

export class HealthCheck {
  private static instance: HealthCheck;
  private client!: Client; // Definite assignment assertion - set via setClient()
  private readonly config: ConfigManager;
  private readonly shapes: ShapesIntegration;
  private readonly startTime: Date;

  private constructor() {
    this.config = ConfigManager.getInstance();
    this.shapes = ShapesIntegration.getInstance();
    this.startTime = new Date();
  }

  public static getInstance(): HealthCheck {
    if (!HealthCheck.instance) {
      HealthCheck.instance = new HealthCheck();
    }
    return HealthCheck.instance;
  }

  public setClient(client: Client): void {
    this.client = client;
  }

  public async performHealthCheck(): Promise<HealthStatus> {
    const components = {
      discord: await this.checkDiscordHealth(),
      config: await this.checkConfigHealth(),
      shapes: await this.checkShapesHealth(),
      memory: await this.checkMemoryHealth(),
      commands: await this.checkCommandsHealth()
    };

    const overall = this.determineOverallHealth(components);

    return {
      overall,
      components,
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime.getTime()
    };
  }

  private async checkDiscordHealth(): Promise<ComponentHealth> {
    try {
      if (!this.client) {
        return {
          status: 'unhealthy',
          message: 'Discord client not initialized'
        };
      }

      if (!this.client.isReady()) {
        return {
          status: 'unhealthy',
          message: 'Discord client not ready'
        };
      }

      const ping = this.client.ws.ping;
      
      if (ping > 1000) {
        return {
          status: 'degraded',
          message: 'High Discord API latency',
          details: { ping }
        };
      }

      if (ping > 500) {
        return {
          status: 'degraded',
          message: 'Moderate Discord API latency',
          details: { ping }
        };
      }

      return {
        status: 'healthy',
        message: 'Discord connection stable',
        details: { 
          ping,
          guilds: this.client.guilds.cache.size,
          users: this.client.users.cache.size
        }
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: 'Discord health check failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  private async checkConfigHealth(): Promise<ComponentHealth> {
    try {
      const settings = this.config.getSettings();

      // Check required settings
      if (!settings.prefix || settings.prefix.length === 0) {
        return {
          status: 'unhealthy',
          message: 'Invalid prefix configuration'
        };
      }

      if (!Array.isArray(settings.whitelist)) {
        return {
          status: 'unhealthy',
          message: 'Invalid whitelist configuration'
        };
      }

      if (!process.env.DISCORD_TOKEN) {
        return {
          status: 'unhealthy',
          message: 'Discord token not configured'
        };
      }

      return {
        status: 'healthy',
        message: 'Configuration valid',
        details: {
          prefix: settings.prefix,
          whitelistSize: settings.whitelist.length,
          aiEnabled: settings.ai_enabled,
          selfbotEnabled: settings.selfbot_enabled
        }
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: 'Configuration check failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  private async checkShapesHealth(): Promise<ComponentHealth> {
    try {
      const settings = this.config.getSettings();

      if (!settings.shapes_api_key || !settings.shapes_character_id) {
        return {
          status: 'degraded',
          message: 'Shapes.inc not configured (using local fallback)',
          details: { fallbackMode: true }
        };
      }

      // Test Shapes.inc API connectivity
      try {
        const testResponse = await axios.get('https://api.shapes.inc/v1/health', {
          headers: {
            'Authorization': `Bearer ${settings.shapes_api_key}`
          },
          timeout: 5000
        });

        if (testResponse.status === 200) {
          return {
            status: 'healthy',
            message: 'Shapes.inc API accessible',
            details: { apiStatus: 'connected' }
          };
        }
      } catch (apiError) {
        return {
          status: 'degraded',
          message: 'Shapes.inc API unreachable (using local fallback)',
          details: { 
            fallbackMode: true,
            error: apiError instanceof Error ? apiError.message : 'API error'
          }
        };
      }

      return {
        status: 'healthy',
        message: 'Shapes.inc configured and accessible'
      };

    } catch (error) {
      return {
        status: 'degraded',
        message: 'Shapes.inc health check failed (using local fallback)',
        details: { 
          fallbackMode: true,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  private async checkMemoryHealth(): Promise<ComponentHealth> {
    try {
      const memoryUsage = process.memoryUsage();
      const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
      const heapTotalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
      const rssMB = Math.round(memoryUsage.rss / 1024 / 1024);

      // Alert if memory usage is high
      if (heapUsedMB > 500) {
        return {
          status: 'degraded',
          message: 'High memory usage detected',
          details: { heapUsedMB, heapTotalMB, rssMB }
        };
      }

      if (heapUsedMB > 1000) {
        return {
          status: 'unhealthy',
          message: 'Critical memory usage',
          details: { heapUsedMB, heapTotalMB, rssMB }
        };
      }

      return {
        status: 'healthy',
        message: 'Memory usage normal',
        details: { heapUsedMB, heapTotalMB, rssMB }
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: 'Memory health check failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  private async checkCommandsHealth(): Promise<ComponentHealth> {
    try {
      // This would be implemented when we have access to the command handler
      // For now, return a basic health status
      return {
        status: 'healthy',
        message: 'Command system operational',
        details: { 
          status: 'Commands loaded and ready'
        }
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: 'Command system health check failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  private determineOverallHealth(components: HealthStatus['components']): 'healthy' | 'degraded' | 'unhealthy' {
    const statuses = Object.values(components).map(c => c.status);

    if (statuses.includes('unhealthy')) {
      return 'unhealthy';
    }

    if (statuses.includes('degraded')) {
      return 'degraded';
    }

    return 'healthy';
  }

  public async logHealthStatus(): Promise<void> {
    const health = await this.performHealthCheck();
    
    console.log(`
🏥 Amy Health Check - ${health.overall.toUpperCase()}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏱️  Uptime: ${Math.round(health.uptime / 1000)}s
🤖 Discord: ${this.getStatusEmoji(health.components.discord.status)} ${health.components.discord.message}
⚙️  Config: ${this.getStatusEmoji(health.components.config.status)} ${health.components.config.message}
🔗 Shapes: ${this.getStatusEmoji(health.components.shapes.status)} ${health.components.shapes.message}
💾 Memory: ${this.getStatusEmoji(health.components.memory.status)} ${health.components.memory.message}
📋 Commands: ${this.getStatusEmoji(health.components.commands.status)} ${health.components.commands.message}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    `);
  }

  private getStatusEmoji(status: 'healthy' | 'degraded' | 'unhealthy'): string {
    switch (status) {
      case 'healthy': return '✅';
      case 'degraded': return '⚠️';
      case 'unhealthy': return '❌';
      default: return '❓';
    }
  }
}
