import { Message, Client } from 'discord.js';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';
import { CommandContext } from '../types';

export interface Command {
  name: string;
  aliases?: string[];
  description: string;
  usage: string;
  category: string;
  execute(message: Message, args: string[], context: CommandContext): Promise<void>;
}

export class CommandHandler {
  private static instance: CommandHandler;
  private readonly commands: Map<string, Command> = new Map();
  private readonly aliases: Map<string, string> = new Map();
  private readonly config: ConfigManager;
  private client!: Client; // Definite assignment assertion - set via setClient()

  private constructor() {
    this.config = ConfigManager.getInstance();
  }

  public static getInstance(): CommandHandler {
    if (!CommandHandler.instance) {
      CommandHandler.instance = new CommandHandler();
    }
    return CommandHandler.instance;
  }

  public setClient(client: Client): void {
    this.client = client;
  }

  public registerCommand(command: Command): void {
    this.commands.set(command.name.toLowerCase(), command);
    
    // Register aliases
    if (command.aliases) {
      command.aliases.forEach(alias => {
        this.aliases.set(alias.toLowerCase(), command.name.toLowerCase());
      });
    }

    console.log(`✨ Registered command: ${command.name}`);
  }

  public async handleCommand(message: Message, context: CommandContext): Promise<boolean> {
    const settings = this.config.getSettings();
    const args = message.content.slice(settings.prefix.length).trim().split(/ +/);
    const commandName = args.shift()?.toLowerCase();

    if (!commandName) {
      return false;
    }

    // Check whitelist for commands (except for selfbot command which needs special handling)
    if (!context.isWhitelisted && commandName !== 'selfbot') {
      const denialMessage = AmyPersonality.createDenialMessage();
      await message.reply(denialMessage);
      return true;
    }

    // Resolve command name from alias
    const resolvedName = this.aliases.get(commandName) || commandName;
    const command = this.commands.get(resolvedName);

    if (!command) {
      // Command not found - give helpful suggestion
      const suggestions = this.getSimilarCommands(commandName);
      let response = `I don't know that command, Boss-sama! 🤔`;
      
      if (suggestions.length > 0) {
        response += `\nDid you mean: ${suggestions.map(s => `\`${settings.prefix}${s}\``).join(', ')}?`;
      }
      
      response += `\nUse \`${settings.prefix}help\` to see all commands! ✨`;
      
      await message.reply(AmyPersonality.addPersonalityToMessage(response));
      return true;
    }

    try {
      // Log command usage
      console.log(`🎯 Command executed: ${command.name} by ${context.username} in ${message.guild?.name || 'DM'}`);
      
      // Execute command
      await command.execute(message, args, context);
      return true;

    } catch (error) {
      console.error(`Command execution error (${command.name}):`, error);
      
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
      return true;
    }
  }

  private getSimilarCommands(input: string): string[] {
    const allCommands = Array.from(this.commands.keys());
    const similar: string[] = [];

    // Find commands that start with the input
    allCommands.forEach(cmd => {
      if (cmd.startsWith(input.toLowerCase())) {
        similar.push(cmd);
      }
    });

    // If no exact matches, find commands with similar characters
    if (similar.length === 0) {
      allCommands.forEach(cmd => {
        if (this.calculateSimilarity(input.toLowerCase(), cmd) > 0.6) {
          similar.push(cmd);
        }
      });
    }

    return similar.slice(0, 3); // Return max 3 suggestions
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) {
      return 1.0;
    }
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  public getCommands(): Map<string, Command> {
    return new Map(this.commands);
  }

  public getCommandsByCategory(): Map<string, Command[]> {
    const categories = new Map<string, Command[]>();
    
    this.commands.forEach(command => {
      const category = command.category;
      if (!categories.has(category)) {
        categories.set(category, []);
      }
      categories.get(category)!.push(command);
    });

    return categories;
  }

  public getCommandHelp(commandName: string): Command | null {
    const resolvedName = this.aliases.get(commandName.toLowerCase()) || commandName.toLowerCase();
    return this.commands.get(resolvedName) || null;
  }

  public async displayCommandStats(): Promise<void> {
    const totalCommands = this.commands.size;
    const totalAliases = this.aliases.size;
    const categories = this.getCommandsByCategory();
    
    console.log(`
🎯 Command System Statistics:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📋 Total Commands: ${totalCommands}
🔗 Total Aliases: ${totalAliases}
📁 Categories: ${categories.size}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    `);

    categories.forEach((commands, category) => {
      console.log(`${category}: ${commands.length} commands`);
    });
  }
}
