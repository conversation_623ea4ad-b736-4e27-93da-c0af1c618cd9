import { Client, GatewayIntentBits } from 'discord.js';
import { importx } from '@discordx/importer';
import { AmyBot } from './AmyBot';
import { ConfigManager } from '../utils/ConfigManager';
import { CommandHandler } from './CommandHandler';
import { EventManager } from './EventManager';
import { HealthCheck } from './HealthCheck';

export class CoreSystem {
  private static instance: CoreSystem;
  private amy!: AmyBot;
  private client!: Client;
  private config: ConfigManager;
  private commandHandler: CommandHandler;
  private eventManager: EventManager;
  private healthCheck: HealthCheck;
  private isInitialized: boolean = false;

  private constructor() {
    this.config = ConfigManager.getInstance();
    this.commandHandler = CommandHandler.getInstance();
    this.eventManager = EventManager.getInstance();
    this.healthCheck = HealthCheck.getInstance();
  }

  public static getInstance(): CoreSystem {
    if (!CoreSystem.instance) {
      CoreSystem.instance = new CoreSystem();
    }
    return CoreSystem.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      throw new Error('Core system is already initialized!');
    }

    console.log('🚀 Initializing Amy Core System...');

    try {
      // Step 1: Set up dependency injection
      await this.setupDependencyInjection();

      // Step 2: Import all commands and events
      await this.importCommands();

      // Step 3: Create Discord client
      await this.createDiscordClient();

      // Step 4: Initialize Amy bot
      await this.initializeAmyBot();

      // Step 5: Set up error handling
      await this.setupErrorHandling();

      // Step 6: Validate configuration
      await this.validateConfiguration();

      this.isInitialized = true;
      console.log('✅ Core system initialized successfully!');

    } catch (error) {
      console.error('❌ Failed to initialize core system:', error);
      throw error;
    }
  }

  private async setupDependencyInjection(): Promise<void> {
    console.log('🔧 Setting up dependency injection...');
    // DiscordX v11+ uses a simpler DI setup
    // No explicit engine setup needed for basic functionality
  }

  private async importCommands(): Promise<void> {
    console.log('📦 Importing commands and events...');
    // Import only compiled .js files, exclude .d.ts declaration files
    await importx(`${__dirname}/../commands/**/*.js`);
    console.log('✅ Commands imported successfully!');
  }

  private async createDiscordClient(): Promise<void> {
    console.log('🤖 Creating Discord client...');
    
    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.DirectMessages,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildPresences,
        GatewayIntentBits.GuildEmojisAndStickers, // Note: Deprecated in newer versions
        GatewayIntentBits.GuildVoiceStates
      ],
      partials: [],
      allowedMentions: {
        parse: ['users', 'roles'],
        repliedUser: false
      }
    });

    console.log('✅ Discord client created!');
  }

  private async initializeAmyBot(): Promise<void> {
    console.log('🌟 Initializing Amy bot...');

    this.amy = new AmyBot();
    this.amy.setClient(this.client);
    this.healthCheck.setClient(this.client);

    console.log('✅ Amy bot initialized!');
  }

  private async setupErrorHandling(): Promise<void> {
    console.log('🛡️ Setting up error handling...');

    // Discord client errors
    this.client.on('error', (error) => {
      this.amy.handleError(error);
    });

    // Process errors
    process.on('unhandledRejection', (error) => {
      console.error('🚨 Unhandled promise rejection:', error);
    });

    process.on('uncaughtException', (error) => {
      console.error('🚨 Uncaught exception:', error);
      process.exit(1);
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\n🌟 Shutting down Amy gracefully...');
      await this.shutdown();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('\n🌟 Received SIGTERM, shutting down Amy gracefully...');
      await this.shutdown();
      process.exit(0);
    });

    console.log('✅ Error handling configured!');
  }

  private async validateConfiguration(): Promise<void> {
    console.log('🔍 Validating configuration...');

    const settings = this.config.getSettings();

    // Check required settings
    if (!settings.prefix || settings.prefix.length === 0) {
      throw new Error('Invalid prefix configuration');
    }

    if (!Array.isArray(settings.whitelist)) {
      throw new Error('Invalid whitelist configuration');
    }

    // Validate Discord token
    const token = process.env.DISCORD_TOKEN;
    if (!token) {
      throw new Error('DISCORD_TOKEN is not set in environment variables');
    }

    // Validate Shapes.inc configuration (optional)
    if (settings.shapes_api_key && !settings.shapes_character_id) {
      console.warn('⚠️  Shapes.inc API key provided but character ID is missing');
    }

    console.log('✅ Configuration validated!');
  }

  public async start(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    console.log('🚀 Starting Amy...');

    const token = process.env.DISCORD_TOKEN;
    
    try {
      await this.client.login(token);
      console.log('🎉 Amy has successfully logged in!');
      
      // Display command statistics
      await this.commandHandler.displayCommandStats();

      // Perform initial health check
      await this.healthCheck.logHealthStatus();
      
    } catch (error) {
      console.error('❌ Failed to login to Discord:', error);
      console.log('Please check your DISCORD_TOKEN in the .env file.');
      throw error;
    }
  }

  public async shutdown(): Promise<void> {
    console.log('🌙 Shutting down Amy...');
    
    if (this.amy) {
      await this.amy.shutdown();
    }
    
    console.log('👋 Amy has been shut down gracefully!');
  }

  public getAmy(): AmyBot {
    if (!this.amy) {
      throw new Error('Amy bot is not initialized');
    }
    return this.amy;
  }

  public getClient(): Client {
    if (!this.client) {
      throw new Error('Discord client is not initialized');
    }
    return this.client;
  }

  public getCommandHandler(): CommandHandler {
    return this.commandHandler;
  }

  public getEventManager(): EventManager {
    return this.eventManager;
  }

  public isReady(): boolean {
    return this.isInitialized && this.client?.isReady() === true;
  }

  public getSystemStats(): any {
    if (!this.isReady()) {
      return { status: 'not_ready' };
    }

    return {
      status: 'ready',
      uptime: this.eventManager.getUptime(),
      ...this.amy.getStats(),
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      }
    };
  }

  public getHealthCheck(): HealthCheck {
    return this.healthCheck;
  }

  public async performHealthCheck() {
    return await this.healthCheck.performHealthCheck();
  }

  public async restart(): Promise<void> {
    console.log('🔄 Restarting Amy...');

    await this.shutdown();
    this.isInitialized = false;

    await this.initialize();
    await this.start();

    console.log('✅ Amy restarted successfully!');
  }
}
