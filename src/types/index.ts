export interface BotSettings {
  prefix: string;
  whitelist: string[];
  ai_enabled: boolean;
  selfbot_enabled: boolean;
  shapes_api_key: string;
  shapes_character_id: string;
}

export interface ShapesResponse {
  message: string;
  context?: string;
  emotion?: string;
  continuation?: boolean;
}

export interface CommandContext {
  userId: string;
  username: string;
  channelId: string;
  guildId?: string;
  isWhitelisted: boolean;
}

export interface AnimePersonality {
  greetings: string[];
  reactions: string[];
  emojis: string[];
  slang: string[];
}

export interface StreamMessage {
  content: string;
  delay: number;
  emotion?: string;
}
