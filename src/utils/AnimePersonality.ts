import { AnimePersonality } from '../types';

export class AmyPersonality {
  private static personality: AnimePersonality = {
    greetings: [
      "Yo, <PERSON><PERSON>sa<PERSON>! What's the vibe today? ✨",
      "Hey there, <PERSON><PERSON><PERSON><PERSON>! Ready to slay? 💥",
      "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>! Let's make today lit! 🌟",
      "<PERSON>su<PERSON>, <PERSON><PERSON><PERSON><PERSON>! Time to get this party started! 🎉",
      "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>! What adventure awaits us today? ✨"
    ],
    reactions: [
      "That's so lit, <PERSON><PERSON><PERSON><PERSON>! 🔥",
      "Yooo, that's actually fire! 💥",
      "No cap, that's amazing! ✨",
      "<PERSON><PERSON><PERSON><PERSON>, you're absolutely slaying! 👑",
      "That hits different, desu! 🌟",
      "Period<PERSON>, <PERSON>-sama! 💅",
      "That's a whole vibe! ✨",
      "You're speaking facts, <PERSON>-sama! 📢"
    ],
    emojis: [
      "✨", "💥", "🔥", "🌟", "💅", "👑", "🎉", "💖", 
      "😎", "🥺", "😤", "🙄", "😏", "🤔", "😊", "🤗"
    ],
    slang: [
      "yo", "lit", "slay", "no cap", "periodt", "desu", "<PERSON>-sama",
      "fire", "vibe", "hits different", "lowkey", "highkey", "fr fr",
      "bet", "say less", "that's a mood", "we stan", "iconic"
    ]
  };

  public static getRandomGreeting(): string {
    return this.getRandomFromArray(this.personality.greetings);
  }

  public static getRandomReaction(): string {
    return this.getRandomFromArray(this.personality.reactions);
  }

  public static getRandomEmoji(): string {
    return this.getRandomFromArray(this.personality.emojis);
  }

  public static getRandomSlang(): string {
    return this.getRandomFromArray(this.personality.slang);
  }

  public static addPersonalityToMessage(message: string): string {
    const emoji = this.getRandomEmoji();
    const hasEmoji = Math.random() > 0.3; // 70% chance to add emoji
    
    if (hasEmoji) {
      return `${message} ${emoji}`;
    }
    return message;
  }

  public static createDenialMessage(): string {
    const denials = [
      "Sorry, only Boss-sama and VIPs can use my commands! ✨",
      "Nuh-uh, you're not on the VIP list, desu! 💅",
      "Access denied, but you're still cool! 😎",
      "Only my Boss-sama can command me! 👑",
      "Nice try, but you need VIP status! ✨"
    ];
    return this.getRandomFromArray(denials);
  }

  public static createErrorMessage(): string {
    const errors = [
      "Oopsie! Something went wrong, Boss-sama! 🥺",
      "Uh oh, that didn't work as expected! 😅",
      "Error-chan appeared! Let me try again! 💥",
      "Something's not vibing right, desu! 🤔",
      "Technical difficulties, Boss-sama! 😤"
    ];
    return this.getRandomFromArray(errors);
  }

  private static getRandomFromArray<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  public static createStreamMessages(baseMessage: string, context?: string): string[] {
    const messages = [baseMessage];
    
    // Add contextual follow-up messages
    const followUps = [
      "Things are getting wild out there! 🌟",
      "I've been geeking out over this stuff all day! 💥",
      "Lowkey obsessed with analyzing this, desu! ✨",
      "The vibes are immaculate today! 😎",
      "Boss-sama, this is actually so cool! 🔥"
    ];

    // Randomly add 2-4 follow-up messages
    const numFollowUps = Math.floor(Math.random() * 3) + 2;
    for (let i = 0; i < numFollowUps; i++) {
      messages.push(this.getRandomFromArray(followUps));
    }

    return messages;
  }
}
