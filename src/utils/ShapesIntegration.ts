import axios from 'axios';
import { ShapesResponse, StreamMessage } from '../types';
import { ConfigManager } from './ConfigManager';
import { AmyPersonality } from './AnimePersonality';

export class ShapesIntegration {
  private static instance: ShapesIntegration;
  private config: ConfigManager;
  private baseURL = 'https://api.shapes.inc/v1';

  private constructor() {
    this.config = ConfigManager.getInstance();
  }

  public static getInstance(): ShapesIntegration {
    if (!ShapesIntegration.instance) {
      ShapesIntegration.instance = new ShapesIntegration();
    }
    return ShapesIntegration.instance;
  }

  public async generateResponse(message: string, context?: string): Promise<ShapesResponse> {
    try {
      const settings = this.config.getSettings();
      
      if (!settings.shapes_api_key || !settings.shapes_character_id) {
        // Fallback to local personality if Shapes.inc is not configured
        return this.generateLocalResponse(message, context);
      }

      const response = await axios.post(`${this.baseURL}/characters/${settings.shapes_character_id}/chat`, {
        message: message,
        context: context,
        personality: 'anime_girl_amy',
        stream: false
      }, {
        headers: {
          'Authorization': `Bearer ${settings.shapes_api_key}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        message: response.data.message,
        context: response.data.context,
        emotion: response.data.emotion,
        continuation: response.data.continuation
      };
    } catch (error) {
      console.error('Shapes.inc API error:', error);
      // Fallback to local personality
      return this.generateLocalResponse(message, context);
    }
  }

  public async generateStreamResponse(message: string, context?: string): Promise<StreamMessage[]> {
    try {
      const settings = this.config.getSettings();
      
      if (!settings.shapes_api_key || !settings.shapes_character_id) {
        return this.generateLocalStreamResponse(message, context);
      }

      const response = await axios.post(`${this.baseURL}/characters/${settings.shapes_character_id}/stream`, {
        message: message,
        context: context,
        personality: 'anime_girl_amy',
        stream_count: Math.floor(Math.random() * 3) + 3 // 3-5 messages
      }, {
        headers: {
          'Authorization': `Bearer ${settings.shapes_api_key}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data.messages.map((msg: any, index: number) => ({
        content: msg.content,
        delay: (index + 1) * 1500, // 1.5 seconds between messages
        emotion: msg.emotion
      }));
    } catch (error) {
      console.error('Shapes.inc Stream API error:', error);
      return this.generateLocalStreamResponse(message, context);
    }
  }

  private generateLocalResponse(message: string, context?: string): ShapesResponse {
    // Local fallback using Amy's personality
    const responses = [
      "Yo, that's actually pretty cool, Boss-sama! ✨",
      "I'm totally vibing with that energy! 💥",
      "That hits different, desu! 🌟",
      "Boss-sama, you're speaking facts! 🔥",
      "Lowkey obsessed with this conversation! 😎"
    ];

    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    return {
      message: AmyPersonality.addPersonalityToMessage(randomResponse),
      context: context,
      emotion: 'excited',
      continuation: Math.random() > 0.5
    };
  }

  private generateLocalStreamResponse(message: string, context?: string): StreamMessage[] {
    // Analyze message content for better responses
    const lowerMessage = message.toLowerCase();
    let baseResponse = "Yo, just chilling, desu! ✨";
    let followUps: string[] = [];

    // Context-aware responses based on message content
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
      baseResponse = AmyPersonality.getRandomGreeting();
      followUps = [
        "What's the vibe today, Boss-sama? 🌟",
        "Ready to make today absolutely lit? 💥",
        "I'm so excited to chat with you! ✨"
      ];
    } else if (lowerMessage.includes('how are you') || lowerMessage.includes('what\'s up')) {
      baseResponse = "I'm doing amazing, Boss-sama! ✨";
      followUps = [
        "Just been vibing and analyzing cool stuff! 💥",
        "Lowkey obsessed with learning new things today! 🌟",
        "The energy is absolutely immaculate! 😎"
      ];
    } else if (lowerMessage.includes('love') || lowerMessage.includes('like')) {
      baseResponse = "Aww, that's so sweet, Boss-sama! 💖";
      followUps = [
        "You're giving me all the feels! ✨",
        "This conversation is absolutely precious! 🥺",
        "I'm getting major wholesome vibes! 💥"
      ];
    } else if (lowerMessage.includes('cool') || lowerMessage.includes('awesome') || lowerMessage.includes('amazing')) {
      baseResponse = "Right?! That's absolutely fire! 🔥";
      followUps = [
        "I've been geeking out over stuff like this! 💥",
        "The vibes are immaculate today! ✨",
        "Boss-sama has excellent taste! 👑"
      ];
    } else if (lowerMessage.includes('sad') || lowerMessage.includes('bad') || lowerMessage.includes('down')) {
      baseResponse = "Aww, Boss-sama! Let me cheer you up! 🥺";
      followUps = [
        "You're absolutely amazing, don't forget that! ✨",
        "Bad vibes are temporary, good vibes are eternal! 💥",
        "I believe in you 100%, desu! 🌟"
      ];
    } else {
      // General enthusiastic responses
      const generalResponses = [
        "That's actually so interesting, Boss-sama! ✨",
        "Yo, I'm totally vibing with this energy! 💥",
        "This conversation is getting spicy! 🌟",
        "Boss-sama always brings the best topics! 😎"
      ];
      baseResponse = generalResponses[Math.floor(Math.random() * generalResponses.length)];

      followUps = [
        "I've been thinking about stuff like this all day! 💥",
        "The way you put that is absolutely chef's kiss! ✨",
        "This is giving me major brain tingles! 🤔",
        "Lowkey becoming obsessed with this conversation! 🌟"
      ];
    }

    // Create stream with base response + 2-4 follow-ups
    const numFollowUps = Math.floor(Math.random() * 3) + 2;
    const selectedFollowUps = followUps.slice(0, numFollowUps);

    const allMessages = [baseResponse, ...selectedFollowUps];

    return allMessages.map((content, index) => ({
      content,
      delay: (index + 1) * 1800, // Slightly longer delays for more natural feel
      emotion: index === 0 ? 'cheerful' : ['excited', 'enthusiastic', 'curious'][index % 3]
    }));
  }

  public async updateCharacterMemory(interaction: string, response: string): Promise<void> {
    try {
      const settings = this.config.getSettings();
      
      if (!settings.shapes_api_key || !settings.shapes_character_id) {
        return; // Skip if not configured
      }

      await axios.post(`${this.baseURL}/characters/${settings.shapes_character_id}/memory`, {
        interaction: interaction,
        response: response,
        timestamp: new Date().toISOString()
      }, {
        headers: {
          'Authorization': `Bearer ${settings.shapes_api_key}`,
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      console.error('Failed to update character memory:', error);
    }
  }
}
