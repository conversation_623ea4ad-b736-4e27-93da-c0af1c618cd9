import { User } from 'discord.js';
import { AmyPersonality } from './AnimePersonality';

export interface InteractionData {
  action: string;
  emoji: string;
  messages: string[];
  selfMessages: string[];
  gifs?: string[];
}

export class InteractionUtils {
  private static interactions: Map<string, InteractionData> = new Map([
    ['hug', {
      action: 'hugged',
      emoji: '🤗',
      messages: [
        '{user} gave {target} the warmest hug ever! 🤗✨',
        '{user} wrapped {target} in a cozy hug! So wholesome! 💖',
        'Aww! {user} is giving {target} all the cuddles! 🥺✨',
        '{user} squeezed {target} with maximum love! 🤗💥',
        'Group hug energy! {user} hugged {target}! 🌟'
      ],
      selfMessages: [
        '<PERSON>-sa<PERSON> wants a hug? Here\'s a virtual hug! 🤗✨',
        'Self-hugs are valid! You deserve all the love! 💖',
        'Sending you the biggest virtual hug, <PERSON>-sama! 🥺✨'
      ]
    }],
    ['kiss', {
      action: 'kissed',
      emoji: '😘',
      messages: [
        '{user} gave {target} a sweet kiss! So cute! 😘✨',
        'Smooch! {user} kissed {target}! The romance! 💋',
        '{user} sent {target} kisses! Absolutely adorable! 😘💥',
        'Kiss attack! {user} showered {target} with love! 💖',
        '{user} gave {target} the cutest kiss ever! 🥺✨'
      ],
      selfMessages: [
        'Self-love is important! Here\'s a kiss for you! 😘✨',
        'Boss-sama deserves all the kisses! Mwah! 💋',
        'Virtual kisses coming your way! 😘💥'
      ]
    }],
    ['punch', {
      action: 'punched',
      emoji: '👊',
      messages: [
        '{user} threw a playful punch at {target}! Pow! 👊💥',
        'Anime punch activated! {user} vs {target}! 👊✨',
        '{user} delivered a friendship punch to {target}! 👊😤',
        'Bonk! {user} punched {target} with cartoon physics! 👊🌟',
        '{user} used Friendship Punch on {target}! It\'s super effective! 👊💖'
      ],
      selfMessages: [
        'No self-punching allowed, Boss-sama! That\'s not the vibe! 😤',
        'Self-harm is not kawaii! Get someone else to punch you! 💥',
        'Nuh-uh! No punching yourself! 👊✨'
      ]
    }],
    ['pat', {
      action: 'patted',
      emoji: '👋',
      messages: [
        '{user} gently patted {target}\'s head! So sweet! 👋✨',
        'Head pats incoming! {user} patted {target}! 🥺💥',
        '{user} gave {target} the softest head pats! Adorable! 👋💖',
        'Pat pat! {user} is being so gentle with {target}! 👋🌟',
        '{user} blessed {target} with premium head pats! 👋😊'
      ],
      selfMessages: [
        'Self-pats are totally valid! Pat pat! 👋✨',
        'You deserve all the head pats, Boss-sama! 🥺💥',
        'Virtual head pats for the best Boss-sama! 👋💖'
      ]
    }],
    ['poke', {
      action: 'poked',
      emoji: '👉',
      messages: [
        '{user} poked {target}! Boop! 👉✨',
        'Poke poke! {user} is being playful with {target}! 👉💥',
        '{user} gave {target} a cute little poke! 👉🥺',
        'Boop! {user} poked {target}\'s cheek! So cute! 👉💖',
        '{user} used Poke! {target} noticed them! 👉🌟'
      ],
      selfMessages: [
        'Self-poking? That\'s... interesting! Boop! 👉✨',
        'Boss-sama poked themselves! Cute! 👉💥',
        'Self-boop activated! 👉🥺'
      ]
    }],
    ['highfive', {
      action: 'high-fived',
      emoji: '🙏',
      messages: [
        '{user} gave {target} an epic high-five! 🙏💥',
        'High-five! {user} and {target} are vibing! 🙏✨',
        '{user} slapped hands with {target}! Teamwork! 🙏🌟',
        'Perfect high-five! {user} and {target} nailed it! 🙏😎',
        '{user} and {target} shared the most satisfying high-five! 🙏💖'
      ],
      selfMessages: [
        'Self-high-five? That\'s just clapping, Boss-sama! 👏✨',
        'Here\'s a virtual high-five! 🙏💥',
        'Boss-sama deserves the best high-fives! 🙏🌟'
      ]
    }],
    ['tickle', {
      action: 'tickled',
      emoji: '🤭',
      messages: [
        '{user} tickled {target}! Giggles everywhere! 🤭✨',
        'Tickle attack! {user} made {target} laugh! 🤭💥',
        '{user} found {target}\'s ticklish spot! So cute! 🤭🥺',
        'Tickle fight! {user} vs {target}! 🤭🌟',
        '{user} unleashed tickle chaos on {target}! 🤭💖'
      ],
      selfMessages: [
        'Self-tickling is scientifically impossible! But nice try! 🤭✨',
        'You can\'t tickle yourself, Boss-sama! Get someone else! 🤭💥',
        'Self-tickle attempt failed! Physics says no! 🤭🌟'
      ]
    }]
  ]);

  public static getInteraction(command: string): InteractionData | undefined {
    return this.interactions.get(command.toLowerCase());
  }

  public static formatInteractionMessage(
    interaction: InteractionData,
    user: User,
    target?: User
  ): string {
    if (!target || target.id === user.id) {
      // Self-interaction or no target
      const selfMessage = interaction.selfMessages[
        Math.floor(Math.random() * interaction.selfMessages.length)
      ];
      return AmyPersonality.addPersonalityToMessage(selfMessage);
    }

    // Normal interaction
    const message = interaction.messages[
      Math.floor(Math.random() * interaction.messages.length)
    ];
    
    const formattedMessage = message
      .replace('{user}', `**${user.username}**`)
      .replace('{target}', `**${target.username}**`);
    
    return AmyPersonality.addPersonalityToMessage(formattedMessage);
  }

  public static getAllInteractionCommands(): string[] {
    return Array.from(this.interactions.keys());
  }

  public static createInteractionEmbed(user: User, target: User | null, interaction: InteractionData): any {
    const title = target && target.id !== user.id 
      ? `${interaction.emoji} ${user.username} ${interaction.action} ${target.username}!`
      : `${interaction.emoji} ${user.username} tried to ${interaction.action.slice(0, -2)} themselves!`;

    return {
      title,
      color: 0xFF69B4,
      description: this.formatInteractionMessage(interaction, user, target || undefined),
      footer: {
        text: 'Anime interactions powered by Amy ✨',
        icon_url: user.displayAvatarURL()
      },
      timestamp: new Date().toISOString()
    };
  }

  public static getRandomReaction(): string {
    const reactions = [
      "The wholesome energy is off the charts! 💖",
      "This is giving me all the feels! 🥺",
      "Absolutely adorable interaction! ✨",
      "The friendship vibes are immaculate! 🌟",
      "This is so cute I might cry! 😭💥",
      "Peak wholesome content right here! 💅",
      "My heart can't handle this cuteness! 💖",
      "This interaction is absolutely blessed! ✨"
    ];
    
    return reactions[Math.floor(Math.random() * reactions.length)];
  }
}
