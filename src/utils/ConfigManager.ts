import fs from 'fs';
import path from 'path';
import { BotSettings } from '../types';

export class ConfigManager {
  private static instance: ConfigManager;
  private configPath: string;
  private settings: BotSettings;

  private constructor() {
    this.configPath = path.join(process.cwd(), 'settings.json');
    this.loadSettings();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private loadSettings(): void {
    try {
      const data = fs.readFileSync(this.configPath, 'utf8');
      this.settings = JSON.parse(data);
    } catch (error) {
      console.error('Failed to load settings:', error);
      this.createDefaultSettings();
    }
  }

  private createDefaultSettings(): void {
    this.settings = {
      prefix: 'a!',
      whitelist: [],
      ai_enabled: false,
      selfbot_enabled: true,
      shapes_api_key: '',
      shapes_character_id: ''
    };
    this.saveSettings();
  }

  private saveSettings(): void {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(this.settings, null, 2));
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }

  public getSettings(): BotSettings {
    return { ...this.settings };
  }

  public updateSetting<K extends keyof BotSettings>(key: K, value: BotSettings[K]): void {
    this.settings[key] = value;
    this.saveSettings();
  }

  public isWhitelisted(userId: string): boolean {
    return this.settings.whitelist.includes(userId);
  }

  public addToWhitelist(userId: string): void {
    if (!this.isWhitelisted(userId)) {
      this.settings.whitelist.push(userId);
      this.saveSettings();
    }
  }

  public removeFromWhitelist(userId: string): void {
    const index = this.settings.whitelist.indexOf(userId);
    if (index > -1) {
      this.settings.whitelist.splice(index, 1);
      this.saveSettings();
    }
  }
}
