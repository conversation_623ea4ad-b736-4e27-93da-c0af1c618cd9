import { createCanvas, loadImage, Canvas, CanvasRenderingContext2D } from 'canvas';
import axios from 'axios';

export class CanvasUtils {
  
  public static async createAvatarDisplay(avatarUrl: string, username: string): Promise<Buffer> {
    const canvas = createCanvas(400, 500);
    const ctx = canvas.getContext('2d');

    // Background gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, 500);
    gradient.addColorStop(0, '#FF69B4');
    gradient.addColorStop(1, '#FF1493');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 400, 500);

    // Anime-style decorative elements
    ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
    for (let i = 0; i < 20; i++) {
      const x = Math.random() * 400;
      const y = Math.random() * 500;
      const size = Math.random() * 10 + 5;
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fill();
    }

    try {
      // Load and draw avatar
      const avatar = await loadImage(avatarUrl);
      
      // Create circular mask for avatar
      ctx.save();
      ctx.beginPath();
      ctx.arc(200, 180, 120, 0, Math.PI * 2);
      ctx.clip();
      
      // Draw avatar
      ctx.drawImage(avatar, 80, 60, 240, 240);
      ctx.restore();

      // Avatar border with anime style
      ctx.strokeStyle = '#FFFFFF';
      ctx.lineWidth = 8;
      ctx.beginPath();
      ctx.arc(200, 180, 120, 0, Math.PI * 2);
      ctx.stroke();

      // Sparkle effects around avatar
      ctx.fillStyle = '#FFFFFF';
      const sparkles = [
        { x: 120, y: 100, size: 8 },
        { x: 280, y: 120, size: 6 },
        { x: 100, y: 200, size: 10 },
        { x: 300, y: 180, size: 7 },
        { x: 150, y: 280, size: 9 },
        { x: 250, y: 290, size: 5 }
      ];

      sparkles.forEach(sparkle => {
        this.drawSparkle(ctx, sparkle.x, sparkle.y, sparkle.size);
      });

      // Username text with anime styling
      ctx.fillStyle = '#FFFFFF';
      ctx.font = 'bold 32px Arial';
      ctx.textAlign = 'center';
      ctx.strokeStyle = '#FF1493';
      ctx.lineWidth = 3;
      ctx.strokeText(username, 200, 380);
      ctx.fillText(username, 200, 380);

      // Cute subtitle
      ctx.font = '20px Arial';
      ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
      ctx.fillText('✨ Kawaii Avatar ✨', 200, 420);

    } catch (error) {
      console.error('Error loading avatar:', error);
      // Fallback design
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '24px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Avatar not found! 🥺', 200, 250);
    }

    return canvas.toBuffer('image/png');
  }

  public static async createPetPetGif(avatarUrl: string): Promise<Buffer> {
    // For now, create a static image. GIF creation would require additional libraries
    const canvas = createCanvas(300, 300);
    const ctx = canvas.getContext('2d');

    // Cute background
    const gradient = ctx.createRadialGradient(150, 150, 0, 150, 150, 150);
    gradient.addColorStop(0, '#FFB6C1');
    gradient.addColorStop(1, '#FF69B4');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 300, 300);

    try {
      const avatar = await loadImage(avatarUrl);
      
      // Draw slightly squished avatar (petpet effect)
      ctx.save();
      ctx.beginPath();
      ctx.arc(150, 140, 80, 0, Math.PI * 2);
      ctx.clip();
      ctx.drawImage(avatar, 70, 50, 160, 180); // Slightly taller for squish effect
      ctx.restore();

      // Hand petting
      ctx.fillStyle = '#FDBCB4';
      ctx.beginPath();
      ctx.ellipse(150, 60, 40, 25, 0, 0, Math.PI * 2);
      ctx.fill();

      // Fingers
      for (let i = 0; i < 4; i++) {
        ctx.beginPath();
        ctx.ellipse(130 + i * 10, 45, 8, 15, 0, 0, Math.PI * 2);
        ctx.fill();
      }

      // Pet pet text
      ctx.fillStyle = '#FFFFFF';
      ctx.font = 'bold 24px Arial';
      ctx.textAlign = 'center';
      ctx.strokeStyle = '#FF1493';
      ctx.lineWidth = 2;
      ctx.strokeText('Pet Pet! ✨', 150, 260);
      ctx.fillText('Pet Pet! ✨', 150, 260);

    } catch (error) {
      console.error('Error creating petpet:', error);
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '20px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Petpet failed! 🥺', 150, 150);
    }

    return canvas.toBuffer('image/png');
  }

  public static async createSlapImage(targetAvatarUrl: string, slapperName: string, targetName: string): Promise<Buffer> {
    const canvas = createCanvas(500, 300);
    const ctx = canvas.getContext('2d');

    // Dynamic background
    const gradient = ctx.createLinearGradient(0, 0, 500, 300);
    gradient.addColorStop(0, '#FF4500');
    gradient.addColorStop(1, '#FF6347');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 500, 300);

    // Action lines for anime effect
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 3;
    for (let i = 0; i < 10; i++) {
      ctx.beginPath();
      ctx.moveTo(200 + i * 15, 50);
      ctx.lineTo(250 + i * 15, 100);
      ctx.stroke();
    }

    try {
      const targetAvatar = await loadImage(targetAvatarUrl);
      
      // Draw target avatar (being slapped)
      ctx.save();
      ctx.beginPath();
      ctx.arc(380, 150, 60, 0, Math.PI * 2);
      ctx.clip();
      ctx.drawImage(targetAvatar, 320, 90, 120, 120);
      ctx.restore();

      // Slap hand
      ctx.fillStyle = '#FDBCB4';
      ctx.beginPath();
      ctx.ellipse(280, 150, 30, 50, -0.3, 0, Math.PI * 2);
      ctx.fill();

      // Impact effect
      ctx.fillStyle = '#FFFF00';
      ctx.font = 'bold 40px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('SLAP!', 250, 100);

      // Stars for impact
      const stars = [
        { x: 220, y: 120 },
        { x: 280, y: 110 },
        { x: 240, y: 140 },
        { x: 300, y: 130 }
      ];

      ctx.fillStyle = '#FFFFFF';
      stars.forEach(star => {
        this.drawStar(ctx, star.x, star.y, 8);
      });

      // Text
      ctx.fillStyle = '#FFFFFF';
      ctx.font = 'bold 20px Arial';
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 2;
      const text = `${slapperName} slapped ${targetName}! 💥`;
      ctx.strokeText(text, 250, 250);
      ctx.fillText(text, 250, 250);

    } catch (error) {
      console.error('Error creating slap image:', error);
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '20px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Slap failed! 🥺', 250, 150);
    }

    return canvas.toBuffer('image/png');
  }

  private static drawSparkle(ctx: CanvasRenderingContext2D, x: number, y: number, size: number): void {
    ctx.save();
    ctx.translate(x, y);
    
    // Draw 4-pointed star
    ctx.beginPath();
    ctx.moveTo(0, -size);
    ctx.lineTo(size * 0.3, -size * 0.3);
    ctx.lineTo(size, 0);
    ctx.lineTo(size * 0.3, size * 0.3);
    ctx.lineTo(0, size);
    ctx.lineTo(-size * 0.3, size * 0.3);
    ctx.lineTo(-size, 0);
    ctx.lineTo(-size * 0.3, -size * 0.3);
    ctx.closePath();
    ctx.fill();
    
    ctx.restore();
  }

  private static drawStar(ctx: CanvasRenderingContext2D, x: number, y: number, size: number): void {
    ctx.save();
    ctx.translate(x, y);
    
    ctx.beginPath();
    for (let i = 0; i < 5; i++) {
      const angle = (i * 144 - 90) * Math.PI / 180;
      const x = Math.cos(angle) * size;
      const y = Math.sin(angle) * size;
      if (i === 0) ctx.moveTo(x, y);
      else ctx.lineTo(x, y);
    }
    ctx.closePath();
    ctx.fill();
    
    ctx.restore();
  }
}
