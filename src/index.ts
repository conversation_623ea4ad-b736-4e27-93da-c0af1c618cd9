import 'dotenv/config';
import { CoreSystem } from './AI/CoreSystem';

async function main(): Promise<void> {
  const coreSystem = CoreSystem.getInstance();

  try {
    await coreSystem.start();
  } catch (error) {
    console.error('❌ Failed to start Amy:', error);
    process.exit(1);
  }
}

// Start Amy
main();

// Display startup message
console.log(`
🌟✨ Amy Selfbot Starting Up ✨🌟
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💖 Anime Girl Personality: Activated
🤖 AI Stream Responses: Ready
🔧 Shapes.inc Integration: Configured
⚡ Commands: Loading...
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⚠️  SELFBOT WARNING: Use responsibly!
    This violates Discord's ToS.
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
`);
