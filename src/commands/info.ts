import { Message, EmbedBuilder, Guild } from 'discord.js';
import { Discord, On } from 'discordx';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';

@Discord()
export class InfoCommand {
  private config = ConfigManager.getInstance();

  @On({ event: 'messageCreate' })
  async onMessage(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!message.content.startsWith(settings.prefix + 'info')) {
      return;
    }

    // Check whitelist
    if (!this.config.isWhitelisted(message.author.id)) {
      const denialMessage = AmyPersonality.createDenialMessage();
      await message.reply(denialMessage);
      return;
    }

    try {
      if (!message.guild) {
        await message.reply(AmyPersonality.addPersonalityToMessage(
          "This command only works in servers, Boss-sama! 💥"
        ));
        return;
      }

      const guild = message.guild;
      await guild.members.fetch(); // Fetch all members for accurate counts

      const embed = new EmbedBuilder()
        .setTitle(`🌟 ${guild.name} Server Info ✨`)
        .setDescription('Here\'s the tea on this server, Boss-sama! 💅')
        .setColor(0xFF69B4)
        .setThumbnail(guild.iconURL() || '')
        .addFields([
          {
            name: '👑 Server Owner',
            value: `<@${guild.ownerId}>`,
            inline: true
          },
          {
            name: '📅 Created',
            value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:F>`,
            inline: true
          },
          {
            name: '🆔 Server ID',
            value: guild.id,
            inline: true
          },
          {
            name: '👥 Members',
            value: `**Total:** ${guild.memberCount}\n**Online:** ${guild.members.cache.filter(m => m.presence?.status !== 'offline').size}`,
            inline: true
          },
          {
            name: '📝 Channels',
            value: `**Total:** ${guild.channels.cache.size}\n**Text:** ${guild.channels.cache.filter(c => c.type === 0).size}\n**Voice:** ${guild.channels.cache.filter(c => c.type === 2).size}`,
            inline: true
          },
          {
            name: '🎭 Roles',
            value: `${guild.roles.cache.size} roles`,
            inline: true
          },
          {
            name: '😀 Emojis',
            value: `${guild.emojis.cache.size} emojis`,
            inline: true
          },
          {
            name: '🔒 Verification Level',
            value: this.getVerificationLevel(guild.verificationLevel),
            inline: true
          },
          {
            name: '🛡️ Boost Status',
            value: `**Level:** ${guild.premiumTier}\n**Boosts:** ${guild.premiumSubscriptionCount || 0}`,
            inline: true
          }
        ])
        .setFooter({ 
          text: `Requested by ${message.author.username} | Amy ✨`,
          iconURL: message.author.displayAvatarURL()
        })
        .setTimestamp();

      if (guild.banner) {
        embed.setImage(guild.bannerURL({ size: 1024 }));
      }

      const infoMessages = [
        "Here's all the server deets, Boss-sama! ✨",
        "Server info coming right up, desu! 💥",
        "All the juicy server details! 🌟",
        "Your server breakdown is ready! 💖"
      ];

      const randomMessage = infoMessages[Math.floor(Math.random() * infoMessages.length)];
      
      await message.reply({
        content: AmyPersonality.addPersonalityToMessage(randomMessage),
        embeds: [embed]
      });

    } catch (error) {
      console.error('Info command error:', error);
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
    }
  }

  private getVerificationLevel(level: number): string {
    const levels = {
      0: 'None',
      1: 'Low',
      2: 'Medium',
      3: 'High',
      4: 'Very High'
    };
    return levels[level as keyof typeof levels] || 'Unknown';
  }
}
