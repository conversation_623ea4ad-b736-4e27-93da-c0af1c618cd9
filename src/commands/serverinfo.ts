import { Message, EmbedBuilder } from 'discord.js';
import { Discord, On } from 'discordx';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';

@Discord()
export class ServerInfoCommand {
  private config = ConfigManager.getInstance();

  @On({ event: 'messageCreate' })
  async onMessage(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!message.content.startsWith(settings.prefix + 'serverinfo')) {
      return;
    }

    // Check whitelist
    if (!this.config.isWhitelisted(message.author.id)) {
      const denialMessage = AmyPersonality.createDenialMessage();
      await message.reply(denialMessage);
      return;
    }

    if (!message.guild) {
      await message.reply(AmyPersonality.addPersonalityToMessage(
        "This command only works in servers, Boss-sama! 💥"
      ));
      return;
    }

    try {
      const args = message.content.slice(settings.prefix.length + 'serverinfo'.length).trim().split(/ +/);
      const subcommand = args[0]?.toLowerCase();

      switch (subcommand) {
        case 'role':
        case 'roles':
          await this.handleRoleInfo(message);
          break;
        case 'channel':
        case 'channels':
          await this.handleChannelInfo(message);
          break;
        case 'member':
        case 'members':
          await this.handleMemberInfo(message);
          break;
        default:
          await this.handleGeneralInfo(message);
          break;
      }

    } catch (error) {
      console.error('ServerInfo command error:', error);
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
    }
  }

  private async handleRoleInfo(message: Message): Promise<void> {
    const guild = message.guild!;
    const roles = guild.roles.cache.sort((a, b) => b.position - a.position);

    const embed = new EmbedBuilder()
      .setTitle(`🎭 ${guild.name} - Roles Info ✨`)
      .setDescription(`Total roles: **${roles.size}** (including @everyone)`)
      .setColor(0xFF69B4)
      .setThumbnail(guild.iconURL() || '');

    // Show top 10 roles
    const topRoles = roles.first(10);
    if (topRoles.length > 0) {
      embed.addFields([{
        name: '🔝 Top Roles',
        value: topRoles.map(role => 
          `${role.name === '@everyone' ? '@everyone' : `<@&${role.id}>`} (${role.members.size} members)`
        ).join('\n'),
        inline: false
      }]);
    }

    await message.reply({
      content: AmyPersonality.addPersonalityToMessage("Here's the role breakdown, Boss-sama! 🎭"),
      embeds: [embed]
    });
  }

  private async handleChannelInfo(message: Message): Promise<void> {
    const guild = message.guild!;
    const channels = guild.channels.cache;

    const textChannels = channels.filter(c => c.type === 0).size;
    const voiceChannels = channels.filter(c => c.type === 2).size;
    const categories = channels.filter(c => c.type === 4).size;
    const announcements = channels.filter(c => c.type === 5).size;
    const threads = channels.filter(c => c.type === 11 || c.type === 12).size;

    const embed = new EmbedBuilder()
      .setTitle(`📝 ${guild.name} - Channels Info ✨`)
      .setDescription(`Total channels: **${channels.size}**`)
      .setColor(0xFF69B4)
      .setThumbnail(guild.iconURL() || '')
      .addFields([
        {
          name: '📊 Channel Breakdown',
          value: `📝 **Text:** ${textChannels}\n` +
                 `🔊 **Voice:** ${voiceChannels}\n` +
                 `📁 **Categories:** ${categories}\n` +
                 `📢 **Announcements:** ${announcements}\n` +
                 `🧵 **Threads:** ${threads}`,
          inline: false
        }
      ]);

    await message.reply({
      content: AmyPersonality.addPersonalityToMessage("Channel stats coming right up, desu! 📝"),
      embeds: [embed]
    });
  }

  private async handleMemberInfo(message: Message): Promise<void> {
    const guild = message.guild!;
    await guild.members.fetch();

    const members = guild.members.cache;
    const bots = members.filter(m => m.user.bot).size;
    const humans = members.filter(m => !m.user.bot).size;
    const online = members.filter(m => m.presence?.status === 'online').size;
    const idle = members.filter(m => m.presence?.status === 'idle').size;
    const dnd = members.filter(m => m.presence?.status === 'dnd').size;
    const offline = members.filter(m => !m.presence || m.presence.status === 'offline').size;

    const embed = new EmbedBuilder()
      .setTitle(`👥 ${guild.name} - Member Stats ✨`)
      .setDescription(`Total members: **${guild.memberCount}**`)
      .setColor(0xFF69B4)
      .setThumbnail(guild.iconURL() || '')
      .addFields([
        {
          name: '🤖 Member Types',
          value: `👤 **Humans:** ${humans}\n🤖 **Bots:** ${bots}`,
          inline: true
        },
        {
          name: '📊 Status Breakdown',
          value: `🟢 **Online:** ${online}\n` +
                 `🟡 **Idle:** ${idle}\n` +
                 `🔴 **DND:** ${dnd}\n` +
                 `⚫ **Offline:** ${offline}`,
          inline: true
        }
      ]);

    await message.reply({
      content: AmyPersonality.addPersonalityToMessage("Member stats are lit, Boss-sama! 👥"),
      embeds: [embed]
    });
  }

  private async handleGeneralInfo(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    const embed = new EmbedBuilder()
      .setTitle('📊 ServerInfo Command Help ✨')
      .setDescription('Choose what server info you want, Boss-sama! 💥')
      .setColor(0xFF69B4)
      .addFields([
        {
          name: '🎭 Available Subcommands',
          value: `\`${settings.prefix}serverinfo role\` - Role information\n` +
                 `\`${settings.prefix}serverinfo channel\` - Channel breakdown\n` +
                 `\`${settings.prefix}serverinfo member\` - Member statistics\n` +
                 `\`${settings.prefix}info\` - General server info`,
          inline: false
        }
      ]);

    await message.reply({
      content: AmyPersonality.addPersonalityToMessage("Pick your info type, desu! 📊"),
      embeds: [embed]
    });
  }
}
