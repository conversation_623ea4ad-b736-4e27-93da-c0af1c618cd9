import { Message } from 'discord.js';
import { Discord, On } from 'discordx';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';

@Discord()
export class PingCommand {
  private config = ConfigManager.getInstance();

  @On({ event: 'messageCreate' })
  async onMessage(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!message.content.startsWith(settings.prefix + 'ping')) {
      return;
    }

    // Check whitelist
    if (!this.config.isWhitelisted(message.author.id)) {
      const denialMessage = AmyPersonality.createDenialMessage();
      await message.reply(denialMessage);
      return;
    }

    try {
      const startTime = Date.now();
      const sentMessage = await message.reply('Calculating ping, Boss-sama... ⚡');
      const endTime = Date.now();
      
      const ping = endTime - startTime;
      const apiLatency = message.client.ws.ping;

      const pingResponses = [
        `Yo, I'm super fast! Ping: **${ping}ms** | API: **${apiLatency}ms** ✨`,
        `Lightning speed, desu! Ping: **${ping}ms** | API: **${apiLatency}ms** 💥`,
        `Boss-sama, I'm vibing at **${ping}ms** | API: **${apiLatency}ms** 🌟`,
        `Speed of light activated! Ping: **${ping}ms** | API: **${apiLatency}ms** ⚡`,
        `Zoom zoom! Ping: **${ping}ms** | API: **${apiLatency}ms** 🚀`
      ];

      const randomResponse = pingResponses[Math.floor(Math.random() * pingResponses.length)];
      
      await sentMessage.edit(AmyPersonality.addPersonalityToMessage(randomResponse));

    } catch (error) {
      console.error('Ping command error:', error);
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
    }
  }
}
