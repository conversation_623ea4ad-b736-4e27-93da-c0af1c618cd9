import { Message } from 'discord.js';
import { Discord, On } from 'discordx';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';

@Discord()
export class SelfbotCommand {
  private config = ConfigManager.getInstance();

  @On({ event: 'messageCreate' })
  async onMessage(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!message.content.startsWith(settings.prefix + 'selfbot')) {
      return;
    }

    // Check whitelist
    if (!this.config.isWhitelisted(message.author.id)) {
      const denialMessage = AmyPersonality.createDenialMessage();
      await message.reply(denialMessage);
      return;
    }

    try {
      const args = message.content.slice(settings.prefix.length + 'selfbot'.length).trim().split(/ +/);
      const action = args[0]?.toLowerCase();

      switch (action) {
        case 'on':
        case 'enable':
          await this.enableSelfbot(message);
          break;
        case 'off':
        case 'disable':
          await this.disableSelfbot(message);
          break;
        case 'status':
          await this.showSelfbotStatus(message);
          break;
        default:
          await this.showSelfbotHelp(message);
          break;
      }

    } catch (error) {
      console.error('Selfbot command error:', error);
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
    }
  }

  private async enableSelfbot(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (settings.selfbot_enabled) {
      await message.reply(AmyPersonality.addPersonalityToMessage(
        "I'm already online and ready to slay, Boss-sama! ✨"
      ));
      return;
    }

    this.config.updateSetting('selfbot_enabled', true);

    const enableMessages = [
      "Amy is back online! Ready to serve, Boss-sama! ✨",
      "Selfbot activated! Time to get this party started! 💥",
      "I'm awake and ready for action, desu! 🌟",
      "Boss-sama's personal assistant is back! 👑",
      "Amy-chan reporting for duty! Let's go! 💖"
    ];

    const randomMessage = enableMessages[Math.floor(Math.random() * enableMessages.length)];
    await message.reply(AmyPersonality.addPersonalityToMessage(randomMessage));

    // Update presence
    await message.client.user?.setPresence({
      activities: [{
        name: 'with Boss-sama ✨',
        type: 0 // Playing
      }],
      status: 'online'
    });
  }

  private async disableSelfbot(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!settings.selfbot_enabled) {
      await message.reply(AmyPersonality.addPersonalityToMessage(
        "I'm already offline, Boss-sama! 🤔"
      ));
      return;
    }

    this.config.updateSetting('selfbot_enabled', false);

    const disableMessages = [
      "Going offline now, Boss-sama! Catch you later! ✨",
      "Amy is taking a break! See you soon, desu! 💤",
      "Selfbot disabled! I'll be sleeping now! 🌙",
      "Time for Amy-chan to rest! Sayonara! 👋",
      "Offline mode activated! Until next time! 💖"
    ];

    const randomMessage = disableMessages[Math.floor(Math.random() * disableMessages.length)];
    await message.reply(AmyPersonality.addPersonalityToMessage(randomMessage));

    // Update presence to idle
    await message.client.user?.setPresence({
      activities: [{
        name: 'sleeping... 💤',
        type: 0 // Playing
      }],
      status: 'idle'
    });
  }

  private async showSelfbotStatus(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    const statusEmoji = settings.selfbot_enabled ? '🟢' : '🔴';
    const statusText = settings.selfbot_enabled ? 'ONLINE' : 'OFFLINE';
    const statusDescription = settings.selfbot_enabled 
      ? 'I\'m active and ready to help!'
      : 'I\'m currently sleeping!';

    await message.reply(AmyPersonality.addPersonalityToMessage(
      `Selfbot Status: ${statusEmoji} **${statusText}**\n${statusDescription} ✨`
    ));
  }

  private async showSelfbotHelp(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    const helpText = `
**🤖 Selfbot Control Help ✨**

\`${settings.prefix}selfbot on\` - Enable Amy (go online)
\`${settings.prefix}selfbot off\` - Disable Amy (go offline)
\`${settings.prefix}selfbot status\` - Check current status

**Note:** When disabled, Amy won't respond to any commands or messages except this one! Use with caution, Boss-sama! 💥
    `;

    await message.reply(AmyPersonality.addPersonalityToMessage(helpText));
  }
}
