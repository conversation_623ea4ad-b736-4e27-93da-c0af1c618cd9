import { Message, TextBasedChannel } from 'discord.js';
import { Discord, On } from 'discordx';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';
import { ShapesIntegration } from '../utils/ShapesIntegration';

@Discord()
export class AICommand {
  private config = ConfigManager.getInstance();
  private shapes = ShapesIntegration.getInstance();

  @On({ event: 'messageCreate' })
  async onMessage(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!message.content.startsWith(settings.prefix + 'ai')) {
      return;
    }

    // Check whitelist
    if (!this.config.isWhitelisted(message.author.id)) {
      const denialMessage = AmyPersonality.createDenialMessage();
      await message.reply(denialMessage);
      return;
    }

    try {
      const args = message.content.slice(settings.prefix.length + 'ai'.length).trim().split(/ +/);
      const action = args[0]?.toLowerCase();

      switch (action) {
        case 'on':
        case 'enable':
          await this.enableAI(message);
          break;
        case 'off':
        case 'disable':
          await this.disableAI(message);
          break;
        case 'status':
          await this.showAIStatus(message);
          break;
        case 'test':
          await this.testAI(message, args.slice(1).join(' '));
          break;
        default:
          await this.showAIHelp(message);
          break;
      }

    } catch (error) {
      console.error('AI command error:', error);
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
    }
  }

  private async enableAI(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (settings.ai_enabled) {
      await message.reply(AmyPersonality.addPersonalityToMessage(
        "AI is already enabled, Boss-sama! I'm ready to chat! ✨"
      ));
      return;
    }

    this.config.updateSetting('ai_enabled', true);

    const enableMessages = [
      "AI mode activated! Time for some stream-of-consciousness vibes! ✨",
      "Yo, AI is now online! Ready to chat up a storm, Boss-sama! 💥",
      "Stream responses enabled! Let's get this conversation flowing! 🌟",
      "AI-chan is awake! Prepare for maximum chattiness, desu! 💖"
    ];

    const randomMessage = enableMessages[Math.floor(Math.random() * enableMessages.length)];
    await message.reply(AmyPersonality.addPersonalityToMessage(randomMessage));

    // Demonstrate with a mini stream
    setTimeout(async () => {
      if (this.isTextBasedChannel(message.channel)) {
        await message.channel.send("This is how I'll respond when AI is on! 💥");
      }
    }, 2000);

    setTimeout(async () => {
      if (this.isTextBasedChannel(message.channel)) {
        await message.channel.send("Multiple messages flowing like a stream! ✨");
      }
    }, 4000);

    setTimeout(async () => {
      if (this.isTextBasedChannel(message.channel)) {
        await message.channel.send("It's going to be absolutely lit, Boss-sama! 🔥");
      }
    }, 6000);
  }

  private async disableAI(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!settings.ai_enabled) {
      await message.reply(AmyPersonality.addPersonalityToMessage(
        "AI is already disabled, Boss-sama! 🤔"
      ));
      return;
    }

    this.config.updateSetting('ai_enabled', false);

    const disableMessages = [
      "AI mode disabled! Back to command-only mode, Boss-sama! ✨",
      "Stream responses turned off! I'll be more chill now, desu! 💥",
      "AI-chan is taking a nap! Commands only from now on! 🌟",
      "Quiet mode activated! No more chatty streams! 💤"
    ];

    const randomMessage = disableMessages[Math.floor(Math.random() * disableMessages.length)];
    await message.reply(AmyPersonality.addPersonalityToMessage(randomMessage));
  }

  private async showAIStatus(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    const statusEmoji = settings.ai_enabled ? '🟢' : '🔴';
    const statusText = settings.ai_enabled ? 'ENABLED' : 'DISABLED';
    const statusDescription = settings.ai_enabled 
      ? 'I\'ll respond with stream-of-consciousness messages!'
      : 'I\'ll only respond to commands!';

    await message.reply(AmyPersonality.addPersonalityToMessage(
      `AI Status: ${statusEmoji} **${statusText}**\n${statusDescription} ✨`
    ));
  }

  private async testAI(message: Message, testMessage: string): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!settings.ai_enabled) {
      await message.reply(AmyPersonality.addPersonalityToMessage(
        "AI is disabled! Enable it first with `a!ai on`, Boss-sama! 💥"
      ));
      return;
    }

    if (!testMessage) {
      testMessage = "This is a test message for Amy's AI!";
    }

    await message.reply(AmyPersonality.addPersonalityToMessage(
      "Testing AI response with your message! ✨"
    ));

    // Generate and send test response
    try {
      const streamMessages = await this.shapes.generateStreamResponse(
        testMessage,
        `Test by ${message.author.username}`
      );

      let totalDelay = 0;
      for (const streamMsg of streamMessages) {
        totalDelay += streamMsg.delay;
        setTimeout(async () => {
          try {
            if (this.isTextBasedChannel(message.channel)) {
              await message.channel.send(`[TEST] ${streamMsg.content}`);
            }
          } catch (error) {
            console.error('Failed to send test AI response:', error);
          }
        }, totalDelay);
      }

    } catch (error) {
      console.error('AI test error:', error);
      await message.reply(AmyPersonality.addPersonalityToMessage(
        "AI test failed! Check the configuration, Boss-sama! 🥺"
      ));
    }
  }

  private async showAIHelp(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    const helpText = `
**🤖 AI Command Help ✨**

\`${settings.prefix}ai on\` - Enable AI stream responses
\`${settings.prefix}ai off\` - Disable AI responses  
\`${settings.prefix}ai status\` - Check AI status
\`${settings.prefix}ai test [message]\` - Test AI with a message

**How AI Works:**
When enabled, I'll respond to mentions, DMs, and occasionally to random messages with a stream-of-consciousness style! Multiple messages will flow naturally, just like a real conversation! 💥

**Powered by Shapes.inc** 🌟
Visit https://shapes.inc/ to customize my personality!
    `;

    await message.reply(AmyPersonality.addPersonalityToMessage(helpText));
  }

  /**
   * Type guard to check if a channel supports text-based operations
   */
  private isTextBasedChannel(channel: any): channel is TextBasedChannel & {
    send(content: string): Promise<Message>;
  } {
    return channel &&
           typeof channel.send === 'function' &&
           channel.type !== undefined &&
           channel.type !== 3; // Exclude PartialGroupDMChannel (type 3)
  }
}
