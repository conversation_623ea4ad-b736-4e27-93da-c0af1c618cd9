import { Message, AttachmentBuilder, User, TextBasedChannel } from 'discord.js';
import { Discord, On } from 'discordx';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';
import { CanvasUtils } from '../utils/CanvasUtils';

@Discord()
export class SlapCommand {
  private config = ConfigManager.getInstance();

  @On({ event: 'messageCreate' })
  async onMessage(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!message.content.startsWith(settings.prefix + 'slap')) {
      return;
    }

    // Check whitelist
    if (!this.config.isWhitelisted(message.author.id)) {
      const denialMessage = AmyPersonality.createDenialMessage();
      await message.reply(denialMessage);
      return;
    }

    try {
      const args = message.content.slice(settings.prefix.length + 'slap'.length).trim().split(/ +/);
      
      if (args.length === 0 || !args[0]) {
        await message.reply(AmyPersonality.addPersonalityToMessage(
          "Who do you want to slap, Boss-sama? Mention someone! 💥"
        ));
        return;
      }

      let targetUser: User | null = null;
      const userMention = args[0];

      // Parse target user
      if (userMention.startsWith('<@') && userMention.endsWith('>')) {
        const userId = userMention.slice(2, -1).replace('!', '');
        try {
          targetUser = await message.client.users.fetch(userId);
        } catch (error) {
          await message.reply(AmyPersonality.addPersonalityToMessage(
            "Couldn't find that user to slap, Boss-sama! 🤔"
          ));
          return;
        }
      }
      // Check if it's a user ID
      else if (/^\d+$/.test(userMention)) {
        try {
          targetUser = await message.client.users.fetch(userMention);
        } catch (error) {
          await message.reply(AmyPersonality.addPersonalityToMessage(
            "That user ID doesn't exist, desu! 🥺"
          ));
          return;
        }
      }
      // Check if it's a username (in guild)
      else if (message.guild) {
        const member = message.guild.members.cache.find(
          m => m.user.username.toLowerCase() === userMention.toLowerCase() ||
               m.displayName.toLowerCase() === userMention.toLowerCase()
        );
        if (member) {
          targetUser = member.user;
        }
      }

      if (!targetUser) {
        await message.reply(AmyPersonality.addPersonalityToMessage(
          "I couldn't find that person to slap! Try mentioning them, Boss-sama! ✨"
        ));
        return;
      }

      // Prevent self-slapping with cute message
      if (targetUser.id === message.author.id) {
        const selfSlapMessages = [
          "Boss-sama, you can't slap yourself! That's not how this works! 🥺",
          "Self-slapping is not allowed, desu! Get someone else to do it! 💥",
          "Nuh-uh, no self-harm allowed here! ✨",
          "Boss-sama, that's not the vibe! Find someone else to slap! 😤"
        ];
        const randomSelfSlap = selfSlapMessages[Math.floor(Math.random() * selfSlapMessages.length)];
        await message.reply(AmyPersonality.addPersonalityToMessage(randomSelfSlap));
        return;
      }

      // Get target's avatar
      const targetAvatarUrl = targetUser.displayAvatarURL({ 
        extension: 'png', 
        size: 256 
      });

      // Create loading message
      const loadingMessages = [
        `Preparing to slap ${targetUser.username}! 💥`,
        `Getting the slap ready, Boss-sama! ✨`,
        `Charging up the ultimate slap attack! 🌟`,
        `Time for some anime-style slapping action! 💅`
      ];
      
      const randomLoading = loadingMessages[Math.floor(Math.random() * loadingMessages.length)];
      const loadingMsg = await message.reply(AmyPersonality.addPersonalityToMessage(randomLoading));

      // Generate slap image
      const slapBuffer = await CanvasUtils.createSlapImage(
        targetAvatarUrl, 
        message.author.username, 
        targetUser.username
      );
      
      // Create attachment
      const attachment = new AttachmentBuilder(slapBuffer, { 
        name: `slap_${targetUser.username}.png` 
      });

      // Success messages with anime drama
      const successMessages = [
        `SLAP! ${message.author.username} just slapped ${targetUser.username}! The drama! 💥`,
        `Ooooh! ${targetUser.username} got slapped by ${message.author.username}! Spicy! ✨`,
        `That's gotta hurt! ${message.author.username} delivered a legendary slap! 🌟`,
        `Anime slap activated! ${targetUser.username} has been slapped into next week! 💅`,
        `The slap heard around the server! ${message.author.username} vs ${targetUser.username}! 🔥`
      ];

      const randomSuccess = successMessages[Math.floor(Math.random() * successMessages.length)];

      await loadingMsg.edit({
        content: AmyPersonality.addPersonalityToMessage(randomSuccess),
        files: [attachment]
      });

      // Optional dramatic follow-up
      const followUps = [
        "The tension is real! 😤",
        "Someone call the medics! 🚑",
        "That was absolutely savage! 💀",
        "The anime physics are strong with this one! ✨"
      ];

      // 30% chance for dramatic follow-up
      if (Math.random() > 0.7) {
        setTimeout(async () => {
          const randomFollowUp = followUps[Math.floor(Math.random() * followUps.length)];
          if (this.isTextBasedChannel(message.channel)) {
            await message.channel.send(AmyPersonality.addPersonalityToMessage(randomFollowUp));
          }
        }, 3000);
      }

    } catch (error) {
      console.error('Slap command error:', error);
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
    }
  }

  private isTextBasedChannel(channel: any): channel is TextBasedChannel & { send: Function } {
    return channel && typeof channel.send === 'function' && channel.type !== 3; // Exclude PartialGroupDMChannel
  }
}
