import { Message, AttachmentBuilder, TextBasedChannel } from 'discord.js';
import { Discord, On } from 'discordx';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';
import { CanvasUtils } from '../utils/CanvasUtils';

@Discord()
export class PetPetCommand {
  private config = ConfigManager.getInstance();

  @On({ event: 'messageCreate' })
  async onMessage(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!message.content.startsWith(settings.prefix + 'petpet')) {
      return;
    }

    // Check whitelist
    if (!this.config.isWhitelisted(message.author.id)) {
      const denialMessage = AmyPersonality.createDenialMessage();
      await message.reply(denialMessage);
      return;
    }

    try {
      // Get user's avatar
      const avatarUrl = message.author.displayAvatarURL({ 
        extension: 'png', 
        size: 256 
      });

      // Create loading message with cute anime reactions
      const loadingMessages = [
        "Preparing the ultimate petpet experience! ✨",
        "Getting ready to pet you, <PERSON>-sama! 💥",
        "Making the cutest petpet ever, desu! 🌟",
        "Time for some kawaii petting action! 💖"
      ];
      
      const randomLoading = loadingMessages[Math.floor(Math.random() * loadingMessages.length)];
      const loadingMsg = await message.reply(AmyPersonality.addPersonalityToMessage(randomLoading));

      // Generate petpet image
      const petpetBuffer = await CanvasUtils.createPetPetGif(avatarUrl);
      
      // Create attachment
      const attachment = new AttachmentBuilder(petpetBuffer, { 
        name: `${message.author.username}_petpet.png` 
      });

      // Success messages with anime girl enthusiasm
      const successMessages = [
        "Pet pet pet! You're so cute, Boss-sama! ✨",
        "Awww, who's a good Boss-sama? You are! 💥",
        "Unlimited petpet power activated! 🌟",
        "This is the cutest thing ever, desu! 💖",
        "Boss-sama deserves all the pets! 🥺✨",
        "Pet pet revolution starts now! 💅"
      ];

      const randomSuccess = successMessages[Math.floor(Math.random() * successMessages.length)];

      await loadingMsg.edit({
        content: AmyPersonality.addPersonalityToMessage(randomSuccess),
        files: [attachment]
      });

      // Send additional cute reactions
      const additionalReactions = [
        "This is giving me life! 💖",
        "So wholesome, I can't even! ✨",
        "Boss-sama is maximum cute! 🥺"
      ];

      // 50% chance to send an additional reaction
      if (Math.random() > 0.5) {
        setTimeout(async () => {
          const randomReaction = additionalReactions[Math.floor(Math.random() * additionalReactions.length)];
          if (this.isTextBasedChannel(message.channel)) {
            await message.channel.send(AmyPersonality.addPersonalityToMessage(randomReaction));
          }
        }, 2000);
      }

    } catch (error) {
      console.error('PetPet command error:', error);
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
    }
  }

  private isTextBasedChannel(channel: any): channel is TextBasedChannel & { send: Function } {
    return channel && typeof channel.send === 'function' && channel.type !== 3; // Exclude PartialGroupDMChannel
  }
}
