import { Message, TextBasedChannel } from 'discord.js';
import { Discord, On } from 'discordx';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';

@Discord()
export class PrefixCommand {
  private config = ConfigManager.getInstance();

  @On({ event: 'messageCreate' })
  async onMessage(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!message.content.startsWith(settings.prefix + 'prefix')) {
      return;
    }

    // Check whitelist
    if (!this.config.isWhitelisted(message.author.id)) {
      const denialMessage = AmyPersonality.createDenialMessage();
      await message.reply(denialMessage);
      return;
    }

    try {
      const args = message.content.slice(settings.prefix.length + 'prefix'.length).trim().split(/ +/);
      const newPrefix = args[0];

      if (!newPrefix) {
        await this.showCurrentPrefix(message);
        return;
      }

      await this.changePrefix(message, newPrefix);

    } catch (error) {
      console.error('Prefix command error:', error);
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
    }
  }

  private async showCurrentPrefix(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    const prefixMessages = [
      `My current prefix is \`${settings.prefix}\`, Boss-sama! ✨`,
      `I'm listening for commands with \`${settings.prefix}\`, desu! 💥`,
      `Current prefix: \`${settings.prefix}\` - Use it to command me! 🌟`,
      `Boss-sama, my prefix is \`${settings.prefix}\`! 👑`
    ];

    const randomMessage = prefixMessages[Math.floor(Math.random() * prefixMessages.length)];
    
    await message.reply(AmyPersonality.addPersonalityToMessage(
      `${randomMessage}\n\nTo change it, use: \`${settings.prefix}prefix <new_prefix>\``
    ));
  }

  private async changePrefix(message: Message, newPrefix: string): Promise<void> {
    const settings = this.config.getSettings();
    const oldPrefix = settings.prefix;

    // Validate new prefix
    if (newPrefix.length > 5) {
      await message.reply(AmyPersonality.addPersonalityToMessage(
        "That prefix is too long, Boss-sama! Keep it under 5 characters! 🤔"
      ));
      return;
    }

    if (newPrefix.includes(' ')) {
      await message.reply(AmyPersonality.addPersonalityToMessage(
        "Prefixes can't have spaces, desu! Try something else! 💥"
      ));
      return;
    }

    // Check for potentially problematic prefixes
    const problematicPrefixes = ['@', '#', ':', '```', '`'];
    if (problematicPrefixes.some(p => newPrefix.includes(p))) {
      await message.reply(AmyPersonality.addPersonalityToMessage(
        "That prefix might cause issues, Boss-sama! Try a simpler one! ✨"
      ));
      return;
    }

    // Update prefix
    this.config.updateSetting('prefix', newPrefix);

    const successMessages = [
      `Prefix changed from \`${oldPrefix}\` to \`${newPrefix}\`! I'm ready, Boss-sama! ✨`,
      `New prefix \`${newPrefix}\` activated! The old \`${oldPrefix}\` is history, desu! 💥`,
      `Prefix update complete! Now using \`${newPrefix}\` instead of \`${oldPrefix}\`! 🌟`,
      `Boss-sama, I'm now listening for \`${newPrefix}\` commands! 👑`,
      `Prefix evolution! \`${oldPrefix}\` → \`${newPrefix}\`! So cool! 💖`
    ];

    const randomMessage = successMessages[Math.floor(Math.random() * successMessages.length)];
    await message.reply(AmyPersonality.addPersonalityToMessage(randomMessage));

    // Send a test message with new prefix
    setTimeout(async () => {
      if (this.isTextBasedChannel(message.channel)) {
        await message.channel.send(AmyPersonality.addPersonalityToMessage(
          `Try the new prefix: \`${newPrefix}ping\` to test it out! 💥`
        ));
      }
    }, 2000);
  }

  private isTextBasedChannel(channel: any): channel is TextBasedChannel & { send: Function } {
    return channel && typeof channel.send === 'function' && channel.type !== 3; // Exclude PartialGroupDMChannel
  }
}
