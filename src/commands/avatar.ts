import { Message, AttachmentBuilder, User } from 'discord.js';
import { Discord, On } from 'discordx';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';
import { CanvasUtils } from '../utils/CanvasUtils';

@Discord()
export class AvatarCommand {
  private config = ConfigManager.getInstance();

  @On({ event: 'messageCreate' })
  async onMessage(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!message.content.startsWith(settings.prefix + 'avatar')) {
      return;
    }

    // Check whitelist
    if (!this.config.isWhitelisted(message.author.id)) {
      const denialMessage = AmyPersonality.createDenialMessage();
      await message.reply(denialMessage);
      return;
    }

    try {
      const args = message.content.slice(settings.prefix.length + 'avatar'.length).trim().split(/ +/);
      let targetUser: User = message.author;

      // Parse target user
      if (args.length > 0 && args[0]) {
        const userMention = args[0];
        
        // Check if it's a mention
        if (userMention.startsWith('<@') && userMention.endsWith('>')) {
          const userId = userMention.slice(2, -1).replace('!', '');
          try {
            targetUser = await message.client.users.fetch(userId);
          } catch (error) {
            await message.reply(AmyPersonality.addPersonalityToMessage(
              "Couldn't find that user, Boss-sama! 🤔"
            ));
            return;
          }
        }
        // Check if it's a user ID
        else if (/^\d+$/.test(userMention)) {
          try {
            targetUser = await message.client.users.fetch(userMention);
          } catch (error) {
            await message.reply(AmyPersonality.addPersonalityToMessage(
              "That user ID doesn't exist, desu! 🥺"
            ));
            return;
          }
        }
        // Check if it's a username (in guild)
        else if (message.guild) {
          const member = message.guild.members.cache.find(
            m => m.user.username.toLowerCase() === userMention.toLowerCase() ||
                 m.displayName.toLowerCase() === userMention.toLowerCase()
          );
          if (member) {
            targetUser = member.user;
          } else {
            await message.reply(AmyPersonality.addPersonalityToMessage(
              "Couldn't find that username, Boss-sama! Try mentioning them instead! ✨"
            ));
            return;
          }
        }
      }

      // Get avatar URL
      const avatarUrl = targetUser.displayAvatarURL({ 
        extension: 'png', 
        size: 512 
      });

      // Create loading message
      const loadingMessages = [
        "Creating kawaii avatar display! ✨",
        "Making it extra cute, desu! 💥",
        "Adding anime sparkles! 🌟",
        "Preparing the perfect avatar! 💖"
      ];
      
      const randomLoading = loadingMessages[Math.floor(Math.random() * loadingMessages.length)];
      const loadingMsg = await message.reply(AmyPersonality.addPersonalityToMessage(randomLoading));

      // Generate avatar image
      const avatarBuffer = await CanvasUtils.createAvatarDisplay(avatarUrl, targetUser.username);
      
      // Create attachment
      const attachment = new AttachmentBuilder(avatarBuffer, { 
        name: `${targetUser.username}_avatar.png` 
      });

      // Success messages
      const successMessages = [
        `Here's ${targetUser.username}'s kawaii avatar, Boss-sama! ✨`,
        `${targetUser.username} looks absolutely adorable! 💥`,
        `Behold the cuteness of ${targetUser.username}! 🌟`,
        `${targetUser.username}'s avatar is serving looks! 💅`
      ];

      const randomSuccess = successMessages[Math.floor(Math.random() * successMessages.length)];

      await loadingMsg.edit({
        content: AmyPersonality.addPersonalityToMessage(randomSuccess),
        files: [attachment]
      });

    } catch (error) {
      console.error('Avatar command error:', error);
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
    }
  }
}
