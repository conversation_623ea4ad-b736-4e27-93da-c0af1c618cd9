import { Message, EmbedBuilder } from 'discord.js';
import { Discord, On } from 'discordx';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';

@Discord()
export class HelpCommand {
  private config = ConfigManager.getInstance();

  @On({ event: 'messageCreate' })
  async onMessage(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    if (!message.content.startsWith(settings.prefix + 'help')) {
      return;
    }

    // Check whitelist
    if (!this.config.isWhitelisted(message.author.id)) {
      const denialMessage = AmyPersonality.createDenialMessage();
      await message.reply(denialMessage);
      return;
    }

    try {
      const embed = new EmbedBuilder()
        .setTitle('🌟 Amy\'s Command Guide ✨')
        .setDescription('Yo, <PERSON>-sa<PERSON>! Here are all my lit commands! 💥')
        .setColor(0xFF69B4) // Hot pink color
        .setThumbnail(message.client.user?.displayAvatarURL() || '')
        .addFields([
          {
            name: '⚡ Basic Commands',
            value: `\`${settings.prefix}ping\` - Check my speed, desu!\n` +
                   `\`${settings.prefix}help\` - Show this guide\n` +
                   `\`${settings.prefix}info\` - Server info vibes`,
            inline: false
          },
          {
            name: '📊 Server Info Commands',
            value: `\`${settings.prefix}serverinfo\` - Full server details\n` +
                   `\`${settings.prefix}serverinfo role\` - Role count\n` +
                   `\`${settings.prefix}serverinfo channel\` - Channel count\n` +
                   `\`${settings.prefix}serverinfo member\` - Member stats`,
            inline: false
          },
          {
            name: '🎨 Image Commands',
            value: `\`${settings.prefix}avatar <user>\` - Show someone's avatar\n` +
                   `\`${settings.prefix}petpet\` - Cute petpet GIF\n` +
                   `\`${settings.prefix}slap <user>\` - Slap someone (playfully!)`,
            inline: false
          },
          {
            name: '💖 Interaction Commands',
            value: `\`${settings.prefix}hug <user>\` - Give warm hugs\n` +
                   `\`${settings.prefix}kiss <user>\` - Send kisses\n` +
                   `\`${settings.prefix}punch <user>\` - Playful punches`,
            inline: false
          },
          {
            name: '⚙️ Configuration',
            value: `\`${settings.prefix}selfbot on/off\` - Toggle Amy\n` +
                   `\`${settings.prefix}ai on/off\` - Toggle AI responses\n` +
                   `\`${settings.prefix}prefix <new>\` - Change prefix`,
            inline: false
          },
          {
            name: '🔗 Shapes.inc Integration',
            value: 'Amy is powered by Shapes.inc for advanced AI!\n' +
                   '[Visit Shapes.inc](https://shapes.inc/) for character customization ✨',
            inline: false
          }
        ])
        .setFooter({ 
          text: 'Created with 💖 by Boss-sama | Powered by Shapes.inc',
          iconURL: message.author.displayAvatarURL()
        })
        .setTimestamp();

      const helpMessages = [
        "Here's your complete guide, Boss-sama! ✨",
        "All my commands in one place, desu! 💥",
        "Your personal Amy manual is here! 🌟",
        "Everything you need to know about me! 💖"
      ];

      const randomMessage = helpMessages[Math.floor(Math.random() * helpMessages.length)];
      
      await message.reply({
        content: AmyPersonality.addPersonalityToMessage(randomMessage),
        embeds: [embed]
      });

    } catch (error) {
      console.error('Help command error:', error);
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
    }
  }
}
