import { Message, EmbedBuilder, User, TextBasedChannel } from 'discord.js';
import { Discord, On } from 'discordx';
import { ConfigManager } from '../utils/ConfigManager';
import { AmyPersonality } from '../utils/AnimePersonality';

interface InteractionData {
  action: string;
  emoji: string;
  messages: string[];
  selfMessages: string[];
}

@Discord()
export class InteractionsCommand {
  private config = ConfigManager.getInstance();

  private interactions: Map<string, InteractionData> = new Map([
    ['hug', {
      action: 'hugged',
      emoji: '🤗',
      messages: [
        '{user} gave {target} the warmest hug ever! 🤗✨',
        '{user} wrapped {target} in a cozy hug! So wholesome! 💖',
        'Aww! {user} is giving {target} all the cuddles! 🥺✨',
        '{user} squeezed {target} with maximum love! 🤗💥',
        'Group hug energy! {user} hugged {target}! 🌟'
      ],
      selfMessages: [
        '<PERSON><PERSON><PERSON><PERSON> wants a hug? Here\'s a virtual hug! 🤗✨',
        'Self-hugs are valid! You deserve all the love! 💖',
        'Sending you the biggest virtual hug, <PERSON>-sama! 🥺✨'
      ]
    }],
    ['kiss', {
      action: 'kissed',
      emoji: '😘',
      messages: [
        '{user} gave {target} a sweet kiss! So cute! 😘✨',
        'Smooch! {user} kissed {target}! The romance! 💋',
        '{user} sent {target} kisses! Absolutely adorable! 😘💥',
        'Kiss attack! {user} showered {target} with love! 💖',
        '{user} gave {target} the cutest kiss ever! 🥺✨'
      ],
      selfMessages: [
        'Self-love is important! Here\'s a kiss for you! 😘✨',
        'Boss-sama deserves all the kisses! Mwah! 💋',
        'Virtual kisses coming your way! 😘💥'
      ]
    }],
    ['punch', {
      action: 'punched',
      emoji: '👊',
      messages: [
        '{user} threw a playful punch at {target}! Pow! 👊💥',
        'Anime punch activated! {user} vs {target}! 👊✨',
        '{user} delivered a friendship punch to {target}! 👊😤',
        'Bonk! {user} punched {target} with cartoon physics! 👊🌟',
        '{user} used Friendship Punch on {target}! It\'s super effective! 👊💖'
      ],
      selfMessages: [
        'No self-punching allowed, Boss-sama! That\'s not the vibe! 😤',
        'Self-harm is not kawaii! Get someone else to punch you! 💥',
        'Nuh-uh! No punching yourself! 👊✨'
      ]
    }],
    ['pat', {
      action: 'patted',
      emoji: '👋',
      messages: [
        '{user} gently patted {target}\'s head! So sweet! 👋✨',
        'Head pats incoming! {user} patted {target}! 🥺💥',
        '{user} gave {target} the softest head pats! Adorable! 👋💖',
        'Pat pat! {user} is being so gentle with {target}! 👋🌟',
        '{user} blessed {target} with premium head pats! 👋😊'
      ],
      selfMessages: [
        'Self-pats are totally valid! Pat pat! 👋✨',
        'You deserve all the head pats, Boss-sama! 🥺💥',
        'Virtual head pats for the best Boss-sama! 👋💖'
      ]
    }],
    ['poke', {
      action: 'poked',
      emoji: '👉',
      messages: [
        '{user} poked {target}! Boop! 👉✨',
        'Poke poke! {user} is being playful with {target}! 👉💥',
        '{user} gave {target} a cute little poke! 👉🥺',
        'Boop! {user} poked {target}\'s cheek! So cute! 👉💖',
        '{user} used Poke! {target} noticed them! 👉🌟'
      ],
      selfMessages: [
        'Self-poking? That\'s... interesting! Boop! 👉✨',
        'Boss-sama poked themselves! Cute! 👉💥',
        'Self-boop activated! 👉🥺'
      ]
    }]
  ]);

  @On({ event: 'messageCreate' })
  async onMessage(message: Message): Promise<void> {
    const settings = this.config.getSettings();
    
    // Check if message starts with any interaction command
    const command = this.getInteractionCommand(message.content, settings.prefix);
    if (!command) {
      return;
    }

    // Check whitelist
    if (!this.config.isWhitelisted(message.author.id)) {
      const denialMessage = AmyPersonality.createDenialMessage();
      await message.reply(denialMessage);
      return;
    }

    try {
      const interaction = this.interactions.get(command);
      if (!interaction) {
        return;
      }

      const args = message.content.slice(settings.prefix.length + command.length).trim().split(/ +/);
      let targetUser: User | null = null;

      // Parse target user if provided
      if (args.length > 0 && args[0]) {
        targetUser = await this.parseUser(message, args[0]);
      }

      await this.executeInteraction(message, interaction, targetUser);

    } catch (error) {
      console.error('Interaction command error:', error);
      const errorMessage = AmyPersonality.createErrorMessage();
      await message.reply(errorMessage);
    }
  }

  private getInteractionCommand(content: string, prefix: string): string | null {
    for (const [command] of this.interactions) {
      if (content.startsWith(prefix + command)) {
        return command;
      }
    }
    return null;
  }

  private async parseUser(message: Message, userInput: string): Promise<User | null> {
    try {
      // Check if it's a mention
      if (userInput.startsWith('<@') && userInput.endsWith('>')) {
        const userId = userInput.slice(2, -1).replace('!', '');
        return await message.client.users.fetch(userId);
      }
      
      // Check if it's a user ID
      if (/^\d+$/.test(userInput)) {
        return await message.client.users.fetch(userInput);
      }
      
      // Check if it's a username (in guild)
      if (message.guild) {
        const member = message.guild.members.cache.find(
          m => m.user.username.toLowerCase() === userInput.toLowerCase() ||
               m.displayName.toLowerCase() === userInput.toLowerCase()
        );
        return member?.user || null;
      }
    } catch (error) {
      console.error('Error parsing user:', error);
    }
    
    return null;
  }

  private async executeInteraction(message: Message, interaction: InteractionData, target: User | null): Promise<void> {
    const embed = new EmbedBuilder()
      .setColor(0xFF69B4)
      .setTimestamp()
      .setFooter({ 
        text: 'Anime interactions powered by Amy ✨',
        iconURL: message.author.displayAvatarURL()
      });

    let interactionMessage: string;
    
    if (!target || target.id === message.author.id) {
      // Self-interaction or no target
      const selfMessage = interaction.selfMessages[
        Math.floor(Math.random() * interaction.selfMessages.length)
      ];
      interactionMessage = AmyPersonality.addPersonalityToMessage(selfMessage);
      embed.setTitle(`${interaction.emoji} ${message.author.username} tried to ${interaction.action.slice(0, -2)} themselves!`);
    } else {
      // Normal interaction
      const messageTemplate = interaction.messages[
        Math.floor(Math.random() * interaction.messages.length)
      ];
      
      interactionMessage = messageTemplate
        .replace('{user}', `**${message.author.username}**`)
        .replace('{target}', `**${target.username}**`);
      
      embed.setTitle(`${interaction.emoji} ${message.author.username} ${interaction.action} ${target.username}!`);
    }

    embed.setDescription(interactionMessage);

    await message.reply({ embeds: [embed] });

    // Random chance for Amy to react
    if (Math.random() > 0.6) {
      const reactions = [
        "The wholesome energy is off the charts! 💖",
        "This is giving me all the feels! 🥺",
        "Absolutely adorable interaction! ✨",
        "The friendship vibes are immaculate! 🌟",
        "This is so cute I might cry! 😭💥"
      ];
      
      const randomReaction = reactions[Math.floor(Math.random() * reactions.length)];
      
      setTimeout(async () => {
        if (this.isTextBasedChannel(message.channel)) {
          await message.channel.send(AmyPersonality.addPersonalityToMessage(randomReaction));
        }
      }, 2000);
    }
  }

  /**
   * Type guard to check if a channel supports text-based operations
   */
  private isTextBasedChannel(channel: any): channel is TextBasedChannel & {
    send(content: string): Promise<Message>;
  } {
    return channel &&
           typeof channel.send === 'function' &&
           channel.type !== undefined &&
           channel.type !== 3; // Exclude PartialGroupDMChannel (type 3)
  }
}
