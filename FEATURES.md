# 🌟 <PERSON> - Complete Features Documentation

This document provides detailed information about all of <PERSON>'s features, commands, and capabilities.

## 🎭 Core Personality Features

### Anime Girl Persona
<PERSON> embodies a cheerful, slightly sassy anime girl character with these traits:
- **Name**: <PERSON><PERSON><PERSON>an
- **Personality**: Enthusiastic, loyal, playful, and caring
- **Speech Pattern**: Mix of Gen Z slang and anime expressions
- **Relationship**: Refers to users as "Boss-sama" (showing respect and affection)

### Signature Expressions
- **Greetings**: "Yo, <PERSON>-sama!", "Ohayo!", "Wassup!"
- **Reactions**: "That's so lit!", "No cap!", "Periodt!", "That hits different, desu!"
- **Emojis**: ✨ 💥 🌟 💖 😎 🥺 😤 🙄 😏 🤔 💅 👑
- **Catchphrases**: "desu", "Boss-sama", "slay", "vibe", "fire", "lowkey/highkey"

## 🤖 AI Stream-of-Consciousness Feature

### Overview
When enabled, <PERSON> responds with multiple connected messages that flow like natural thoughts, powered by Shapes.inc's AI engine.

### How It Works
1. **Trigger Conditions**:
   - Direct mentions (@Amy)
   - Direct messages (DMs)
   - Random responses (5% chance in servers)

2. **Response Generation**:
   - Analyzes message content for context
   - Generates 3-5 related messages
   - Sends with realistic delays (1.5-2 seconds between messages)
   - Maintains anime personality throughout

3. **Example Flow**:
   ```
   User: "what's up?"
   Amy: "Yo, just chilling, desu! ✨"
   Amy: "Things are getting wild out there! 🌟"
   Amy: "I've been geeking out over cool stuff all day!"
   Amy: "Lowkey obsessed with analyzing everything! 💥"
   Amy: "Boss-sama, you're absolutely slaying today! 😎"
   ```

### AI Commands
- `a!ai on` - Enable AI stream responses
- `a!ai off` - Disable AI responses
- `a!ai status` - Check current AI status
- `a!ai test [message]` - Test AI with custom message

## 📋 Command Categories

### ⚡ Basic Commands

#### `a!ping`
**Purpose**: Check Amy's response time and connection quality
**Usage**: `a!ping`
**Response Examples**:
- "Yo, I'm super fast! Ping: **42ms** | API: **28ms** ✨"
- "Lightning speed, desu! Ping: **35ms** | API: **31ms** 💥"
- "Speed of light activated! Ping: **29ms** | API: **25ms** ⚡"

#### `a!help`
**Purpose**: Display comprehensive command guide
**Usage**: `a!help`
**Features**:
- Organized command categories
- Usage examples for each command
- Links to Shapes.inc integration
- Anime-styled embed with personality

#### `a!info`
**Purpose**: Display detailed server information
**Usage**: `a!info`
**Information Provided**:
- Server owner and creation date
- Member counts (total, online, offline)
- Channel breakdown (text, voice, categories)
- Role count and boost status
- Verification level and emoji count

### 📊 Server Information Commands

#### `a!serverinfo`
**Purpose**: Detailed server statistics with subcommands
**Usage**: 
- `a!serverinfo` - Show help for subcommands
- `a!serverinfo role` - Role information and hierarchy
- `a!serverinfo channel` - Channel breakdown by type
- `a!serverinfo member` - Member statistics and status

**Role Information**:
- Total role count
- Top 10 roles by position
- Member count per role
- Hierarchy display

**Channel Information**:
- Text channels count
- Voice channels count
- Categories count
- Announcement channels
- Thread count

**Member Information**:
- Human vs bot breakdown
- Online/idle/DND/offline status
- Activity statistics

### 🎨 Image Generation Commands

#### `a!avatar <user>`
**Purpose**: Create anime-styled avatar displays
**Usage**: 
- `a!avatar` - Show your own avatar
- `a!avatar @user` - Show mentioned user's avatar
- `a!avatar username` - Show user by username
- `a!avatar 123456789` - Show user by ID

**Features**:
- Circular avatar with decorative border
- Anime-style gradient background
- Sparkle effects around avatar
- Username display with stroke effects
- Kawaii subtitle text

#### `a!petpet`
**Purpose**: Generate cute petpet-style images
**Usage**: `a!petpet`
**Features**:
- Uses your avatar as the target
- Cute hand petting animation effect
- Radial gradient background
- "Pet Pet! ✨" text overlay
- Anime-style visual effects

#### `a!slap <user>`
**Purpose**: Create anime-style slap interaction images
**Usage**: `a!slap @user`
**Features**:
- Dynamic action background
- Anime speed lines for effect
- Impact stars and "SLAP!" text
- Target user's avatar
- Dramatic visual styling

**Safety**: Prevents self-slapping with cute denial messages

### 💖 Social Interaction Commands

#### `a!hug <user>`
**Purpose**: Send virtual hugs with anime flair
**Usage**: `a!hug @user` or `a!hug username`
**Response Examples**:
- "{user} gave {target} the warmest hug ever! 🤗✨"
- "{user} wrapped {target} in a cozy hug! So wholesome! 💖"
- "Aww! {user} is giving {target} all the cuddles! 🥺✨"

#### `a!kiss <user>`
**Purpose**: Send sweet kisses
**Usage**: `a!kiss @user`
**Response Examples**:
- "{user} gave {target} a sweet kiss! So cute! 😘✨"
- "Smooch! {user} kissed {target}! The romance! 💋"
- "Kiss attack! {user} showered {target} with love! 💖"

#### `a!punch <user>`
**Purpose**: Playful anime-style punches
**Usage**: `a!punch @user`
**Response Examples**:
- "{user} threw a playful punch at {target}! Pow! 👊💥"
- "Anime punch activated! {user} vs {target}! 👊✨"
- "{user} used Friendship Punch on {target}! It's super effective! 👊💖"

#### `a!pat <user>`
**Purpose**: Gentle head pats
**Usage**: `a!pat @user`
**Response Examples**:
- "{user} gently patted {target}'s head! So sweet! 👋✨"
- "Head pats incoming! {user} patted {target}! 🥺💥"
- "{user} blessed {target} with premium head pats! 👋😊"

#### `a!poke <user>`
**Purpose**: Cute pokes and boops
**Usage**: `a!poke @user`
**Response Examples**:
- "{user} poked {target}! Boop! 👉✨"
- "Poke poke! {user} is being playful with {target}! 👉💥"
- "{user} used Poke! {target} noticed them! 👉🌟"

### ⚙️ Configuration Commands

#### `a!selfbot on/off`
**Purpose**: Toggle Amy's entire activity
**Usage**: 
- `a!selfbot on` - Enable Amy
- `a!selfbot off` - Disable Amy
- `a!selfbot status` - Check current status

**Effects**:
- When disabled, Amy won't respond to any commands except selfbot commands
- Updates Discord presence status
- Persists setting in configuration file

#### `a!prefix <new_prefix>`
**Purpose**: Change command prefix
**Usage**: 
- `a!prefix` - Show current prefix
- `a!prefix !` - Change prefix to "!"
- `a!prefix amy.` - Change prefix to "amy."

**Validation**:
- Maximum 5 characters
- No spaces allowed
- Prevents problematic characters (@, #, :, etc.)
- Immediate effect on all commands

## 🛡️ Security Features

### Whitelist System
- **Access Control**: Only whitelisted users can use commands
- **Denial Messages**: Non-whitelisted users get friendly anime-style denials
- **Configuration**: Managed through `settings.json`
- **Persistence**: Whitelist changes are saved automatically

### Error Handling
- **Graceful Failures**: All errors result in anime-style error messages
- **Fallback Systems**: Shapes.inc failures fall back to local personality
- **Input Validation**: Prevents malformed commands from causing crashes
- **Safe Defaults**: Missing configuration uses safe default values

## 🔧 Shapes.inc Integration

### Character Setup
1. **Visit Shapes.inc**: Create account and character
2. **Configure Personality**: Set as cheerful anime girl
3. **API Integration**: Add credentials to `.env` file
4. **Enable AI**: Use `a!ai on` to activate

### AI Capabilities
- **Context Awareness**: Remembers conversation context
- **Personality Consistency**: Maintains anime girl character
- **Stream Responses**: Multiple connected messages
- **Memory System**: Learns from interactions
- **Fallback Support**: Works without Shapes.inc

## 🎯 Best Practices

### For Users
- **Start with `a!help`** to learn all commands
- **Configure whitelist** before sharing with others
- **Test AI features** in private channels first
- **Use responsibly** - remember this violates Discord ToS

### For Developers
- **Follow anime personality** in all new features
- **Implement whitelist checks** in every command
- **Add error handling** with personality-appropriate messages
- **Test thoroughly** before deploying new commands

This comprehensive feature set makes Amy a unique and engaging Discord companion that brings anime culture and advanced AI to your server interactions! ✨
