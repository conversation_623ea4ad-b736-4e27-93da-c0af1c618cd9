{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "lib": ["ES2022", "DOM"], "types": ["node"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}