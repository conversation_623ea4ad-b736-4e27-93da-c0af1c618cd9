{"name": "amy-self<PERSON>", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/NirussVn0/Selfbot.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/NirussVn0/Selfbot/issues"}, "homepage": "https://github.com/NirussVn0/Selfbot#readme", "description": "", "dependencies": {"@discordx/importer": "^1.3.3", "axios": "^1.10.0", "canvas": "^3.1.2", "discord.js": "^14.21.0", "discordx": "^11.12.5", "dotenv": "^17.2.0", "token-discord-checker": "^1.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "devDependencies": {"@types/node": "^20.0.0"}}