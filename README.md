# 🌟 <PERSON>bot - Anime Girl Discord Assistant ✨

<PERSON> is a vibrant Discord selfbot with an anime girl personality, powered by **Shapes.inc** for advanced AI interactions and built with TypeScript, DiscordX.js, and Canvas.

## ⚠️ Important Disclaimer

**This is a selfbot that violates Discord's Terms of Service.** Use at your own risk. The developers are not responsible for any account suspensions or bans that may result from using this software.

## 🎭 Features

### 🌟 Anime Girl Personality
- Cheerful, slightly sassy anime girl persona (<PERSON>)
- Uses Gen Z slang: "yo," "lit," "slay," "desu," "Boss-sama"
- Dynamic emoticons and reactions: ✨ 💥 🌟 💖 😎
- Consistent anime-style responses across all interactions

### 🤖 AI Stream-of-Consciousness
- **Powered by Shapes.inc** for advanced character AI
- Multi-message stream responses when AI is enabled
- Context-aware conversations that feel natural
- Toggleable AI feature (`a!ai on/off`)

### 🎨 Canvas-Based Image Commands
- Custom avatar displays with anime styling
- PetPet GIF generation with cute animations
- Slap images with anime action effects
- Sparkle effects and kawaii decorations

### 🛡️ Security & Access Control
- Whitelist-based command access control
- Configurable settings stored in `settings.json`
- Safe command validation and error handling

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and pnpm
- Discord account token (use with extreme caution)
- Shapes.inc API key (optional but recommended)

### Installation

1. **Clone and install dependencies:**
```bash
git clone https://github.com/NirussVn0/Selfbot.git
cd Selfbot
pnpm install
```

2. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your Discord token and Shapes.inc credentials
```

3. **Configure settings:**
```bash
# Edit settings.json with your user ID in the whitelist
{
  "prefix": "a!",
  "whitelist": ["YOUR_USER_ID_HERE"],
  "ai_enabled": false,
  "selfbot_enabled": true,
  "shapes_api_key": "YOUR_SHAPES_API_KEY",
  "shapes_character_id": "YOUR_AMY_CHARACTER_ID"
}
```

4. **Build and run:**
```bash
pnpm build
pnpm start

# Or for development:
pnpm dev
```

## 📋 Commands Reference

### ⚡ Basic Commands
- `a!ping` - Check Amy's response time
- `a!help` - Show complete command guide
- `a!info` - Display server information

### 📊 Server Information
- `a!serverinfo` - General server stats
- `a!serverinfo role` - Role information
- `a!serverinfo channel` - Channel breakdown
- `a!serverinfo member` - Member statistics

### 🎨 Image Commands
- `a!avatar <user>` - Show user's avatar with anime styling
- `a!petpet` - Generate cute petpet animation
- `a!slap <user>` - Create anime-style slap image

### 💖 Interaction Commands
- `a!hug <user>` - Give warm hugs
- `a!kiss <user>` - Send sweet kisses
- `a!punch <user>` - Playful anime punches
- `a!pat <user>` - Gentle head pats
- `a!poke <user>` - Cute pokes and boops

### ⚙️ Configuration
- `a!selfbot on/off` - Toggle Amy's activity
- `a!ai on/off` - Toggle AI stream responses
- `a!prefix <new>` - Change command prefix

## 🔧 Shapes.inc Integration

Amy is designed to work with **Shapes.inc** for advanced AI personality:

1. **Visit [Shapes.inc](https://shapes.inc/)**
2. **Create an Amy character** with anime girl personality
3. **Configure API credentials** in your `.env` file
4. **Enable AI mode** with `a!ai on`

### Character Setup Guide
- **Personality**: Cheerful, sassy anime girl
- **Speech patterns**: Gen Z slang, "desu," "Boss-sama"
- **Traits**: Enthusiastic, loyal, slightly dramatic
- **Response style**: Stream-of-consciousness, multiple messages

## 🏗️ Project Structure

```
src/
├── AI/
│   └── AmyBot.ts          # Main bot class with AI integration
├── commands/              # All command implementations
│   ├── ping.ts           # Basic ping command
│   ├── help.ts           # Help and documentation
│   ├── ai.ts             # AI toggle and management
│   ├── avatar.ts         # Avatar display command
│   ├── interactions.ts   # Social interaction commands
│   └── ...
├── utils/                # Utility classes
│   ├── ConfigManager.ts  # Settings management
│   ├── AnimePersonality.ts # Personality system
│   ├── ShapesIntegration.ts # Shapes.inc API
│   └── CanvasUtils.ts    # Image generation
└── types/                # TypeScript definitions
```

## 🛠️ Development

### Building
```bash
pnpm build          # Compile TypeScript
pnpm dev            # Development mode with hot reload
```

### Code Quality
- **TypeScript** for type safety
- **OOP principles** with clean class structure
- **Modular design** for easy extensibility
- **Error handling** with anime personality

## 📝 License

This project is licensed under the ISC License. See LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Follow the anime personality guidelines
4. Test all commands thoroughly
5. Submit a pull request

## 💖 Credits

- **Powered by Shapes.inc** - Advanced AI character platform
- **Built with DiscordX.js** - Modern Discord bot framework
- **Canvas** - Image manipulation and generation
- **Created with love** by the Amy development team ✨

---

**Remember: Use responsibly and at your own risk! Amy is here to bring joy and kawaii vibes to your Discord experience! 🌟**
